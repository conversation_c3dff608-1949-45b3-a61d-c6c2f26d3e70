import {
  Inject,
  Injectable,
  InternalServerErrorException,
  NotImplementedException,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
  ServiceUnavailableException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@hkbu-genai-platform/database/generated/client'; // Import Prisma namespace
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { ChatMessageDto, StreamChatDto } from './dto/stream-chat.dto';
import { Readable } from 'stream';
import * as HttpsProxyAgent from 'https-proxy-agent'; // Added for proxy support
// Remove OperationStatusCodes import as status is compared using string literals
import { ComputerVisionClient } from '@azure/cognitiveservices-computervision'; // Added
import { ApiKeyCredentials } from '@azure/ms-rest-js'; // Added
// Remove convertMultiModelPrompt, keep other needed functions
import {
  removeByIndexes,
  convertGeminiPrompt,
  getModelType,
} from '../utils/prompt-processing';
import { AuthenticatedUser } from '../auth/user.interface'; // Import User interface
import moment from 'moment'; // Correct moment import
import * as _ from 'lodash';
import { MailerService } from '@nestjs-modules/mailer';
import { getHongKongTime } from '../common/utils/timezone.util';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { LlmStreamOptions } from '../llm/llm.service';
import {
  ConversationHistoryItemDto,
  PaginatedConversationHistoryResponseDto,
} from './dto/paginated-history.dto';
import { Query } from '@nestjs/common'; // For DTO in controller, but good to have if service needs to know about query params

// Helper function for sleep
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

@Injectable()
export class GeneralService {
  private readonly logger = new Logger(GeneralService.name);

  constructor(
    private prisma: PrismaService,
    private readonly mailerService: MailerService,
    private configService: ConfigService,
  ) {}

  async handleFeedback(
    feedbackData: CreateFeedbackDto,
    user: { userId: string; email: string },
    ip: string,
  ): Promise<{ message: string; feedbackId?: number }> {
    this.logger.log(`Handling feedback from user ${user.userId} at IP ${ip}`);
    try {
      const feedback = await this.prisma.feedback.create({
        data: {
          ssoid: user.userId,
          ip_address: ip,
          message: feedbackData.content,
          create_by: user.userId,
          create_dt: getHongKongTime(),
        },
      });
      this.logger.log(`Feedback saved with ID: ${feedback.feedback_id}`);

      const adminEmail = this.configService.get<string>('FEEDBACK_ADMIN_EMAIL');
      if (adminEmail) {
        try {
          await this.mailerService.sendMail({
            to: adminEmail,
            subject: `[GenAI Platform (${this.configService.get('NODE_ENV')}) Feedback] (User: ${user.userId})`,
            html: `
              <p><b>User SSOID:</b> ${user.userId}</p>
              <p><b>User Email (from JWT):</b> ${user.email}</p>
              <p><b>User Provided Email (optional):</b> ${feedbackData.email || 'N/A'}</p>
              <p><b>IP Address:</b> ${ip}</p>
              <hr>
              <p><b>Message:</b></p>
              <p>${feedbackData.content.replace(/\n/g, '<br>')}</p>
            `,
          });
          this.logger.log(`Feedback notification sent to ${adminEmail}`);
        } catch (mailError) {
          const message =
            mailError instanceof Error
              ? mailError.message
              : 'Unknown mail error';
          const stack =
            mailError instanceof Error ? mailError.stack : undefined;
          this.logger.error(
            `Failed to send feedback notification to ${adminEmail}: ${message}`,
            stack,
          );
        }
      } else {
        this.logger.warn(
          'FEEDBACK_ADMIN_EMAIL environment variable not set. Skipping admin notification.',
        );
      }
      return {
        message: 'Feedback submitted successfully',
        feedbackId: feedback.feedback_id,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error saving feedback for user ${user.userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to save feedback.');
    }
  }

  async checkTncStatus(userId: string): Promise<{ agreed: boolean }> {
    try {
      const lastAcceptanceRecord = await this.prisma.tnc.findFirst({
        where: {
          ssoid: userId,
          agreed: 'Y',
        },
        orderBy: { create_dt: 'desc' },
      });

      if (!lastAcceptanceRecord) {
        return { agreed: false };
      }

      const lastAcceptanceAsHkt = moment.utc(lastAcceptanceRecord.create_dt);
      const nowHkt = moment().utcOffset(8);

      if (
        lastAcceptanceAsHkt.year() < nowHkt.year() ||
        (lastAcceptanceAsHkt.year() === nowHkt.year() &&
          lastAcceptanceAsHkt.month() < nowHkt.month())
      ) {
        return { agreed: false };
      }

      return { agreed: true };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error checking T&C status for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to check T&C status.');
    }
  }

  async setTncStatus(userId: string): Promise<{ success: boolean }> {
    try {
      const nowHkt = moment().utcOffset(8);
      const fakeUtcDate = new Date(
        Date.UTC(
          nowHkt.year(),
          nowHkt.month(),
          nowHkt.date(),
          nowHkt.hour(),
          nowHkt.minute(),
          nowHkt.second(),
        ),
      );

      await this.prisma.tnc.create({
        data: {
          ssoid: userId,
          agreed: 'Y',
          create_by: 'system',
          create_dt: fakeUtcDate,
        },
      });

      return { success: true };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error setting T&C status for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to set T&C status.');
    }
  }

  async generateApiKey(
    userId: string,
    ip: string,
  ): Promise<{ apiKey: string }> {
    this.logger.log(`Generating API key for user ${userId} from IP ${ip}`);
    const apiKey = uuidv4();
    try {
      await this.prisma.acl_user_api_key.create({
        data: {
          username: userId,
          api_key: apiKey,
          ip_address: ip,
          rec_status: 'A',
          create_by: userId,
          create_dt: getHongKongTime(),
        },
      });
      this.logger.log(`API Key generated and saved for user ${userId}`);
      return { apiKey: apiKey };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error generating API key for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to generate API key.');
    }
  }

  async getNotifyStatus(userId: string): Promise<{ notified: boolean }> {
    this.logger.log(`Checking notify status for user ${userId}`);
    try {
      const notifyRecord = await this.prisma.notify.findFirst({
        where: { ssoid: userId, notified: 'Y' },
        orderBy: { create_dt: 'desc' },
      });
      return { notified: !!notifyRecord };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error checking notify status for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve notification status.',
      );
    }
  }

  // Correctly placed setNotifyStatus method
  async setNotifyStatus(userId: string): Promise<{ success: boolean }> {
    this.logger.log(`Setting notify status for user ${userId}`);
    try {
      const existingNotify = await this.prisma.notify.findFirst({
        where: { ssoid: userId },
        select: { notify_id: true }, // Select the primary key
      });

      if (existingNotify) {
        // Update existing record
        await this.prisma.notify.update({
          where: { notify_id: existingNotify.notify_id }, // Use the primary key for update
          data: {
            notified: 'Y',
            update_by: userId,
            update_dt: getHongKongTime(),
          },
        });
      } else {
        // Create new record
        await this.prisma.notify.create({
          data: {
            ssoid: userId,
            notified: 'Y',
            create_by: userId,
            create_dt: getHongKongTime(),
          },
        });
      }
      return { success: true };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error setting notify status for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to update notification status.',
      );
    }
  }

  async getModelList(
    userId: string,
    userType: string,
  ): Promise<{
    model_list: {
      display_name: string | null;
      model_name: string | null;
      seq: number | null;
      availability_status: string;
    }[];
  }> {
    this.logger.log(
      `Fetching model list for user ${userId} (type: ${userType})`,
    );
    try {
      const models = await this.prisma.model_list.findMany({
        where: {
          OR: [
            { rec_status: 'A' },
            {
              acl_user_access: {
                some: {
                  username: userId,
                  rec_status: 'A',
                  expiry_date: { gt: new Date() },
                },
              },
            },
          ],
          AND: [
            {
              OR: [
                { whitelist: userType },
                { whitelist: { contains: `,${userType},` } },
                { whitelist: { startsWith: `${userType},` } },
                { whitelist: { endsWith: `,${userType}` } },
              ],
            },
            { api_status: 'A' },
          ],
        },
        orderBy: { seq: 'asc' },
        select: {
          display_name: true,
          model_name: true,
          seq: true,
          availability_status: true,
          category: true,
        }, // Added category back
      });
      return { model_list: models };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error fetching model list for user ${userId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to retrieve model list.');
    }
  }

  // Removed deprecated initializeChat method

  async performOcr(
    file: Express.Multer.File,
    user: { userId: string; type: string; dept_unit_code: string },
  ): Promise<{ success: boolean; result: string }> {
    this.logger.log(
      `Performing OCR for user ${user.userId} on file ${file.originalname}`,
    );

    const dept_unit_code = user.dept_unit_code;

    // --- 1. Get Azure Credentials ---
    let visionKey: string | undefined;
    let visionEndpoint: string | undefined;
    const proxyUrl = this.configService.get<string>('HTTPS_PROXY');

    if (dept_unit_code === 'SCE' || dept_unit_code === 'CIE') {
      visionKey = this.configService.get<string>('AZURE_VISION_SCECIE_KEY');
      visionEndpoint = this.configService.get<string>(
        'AZURE_VISION_SCECIE_ENDPOINT',
      );
    } else {
      visionKey = this.configService.get<string>('AZURE_VISION_KEY');
      visionEndpoint = this.configService.get<string>('AZURE_VISION_ENDPOINT');
    }

    if (!visionKey || !visionEndpoint) {
      this.logger.error(
        `Azure Vision credentials not configured for dept_unit_code: ${dept_unit_code}`,
      );
      throw new InternalServerErrorException(
        'OCR service configuration error.',
      );
    }

    // --- 2. Initialize ComputerVisionClient ---
    const credentials = new ApiKeyCredentials({
      inHeader: { 'Ocp-Apim-Subscription-Key': visionKey },
    });
    const clientOptions: any = {}; // Define type if possible
    if (proxyUrl) {
      // Use the specific type from 'https-proxy-agent' if available, or keep 'any'
      clientOptions.requestOptions = {
        agent: new (HttpsProxyAgent as any)(proxyUrl), // Instantiate proxy agent
      };
      this.logger.debug(`Using HTTPS Proxy: ${proxyUrl}`);
    }

    const computerVisionClient = new ComputerVisionClient(
      credentials,
      visionEndpoint,
      clientOptions,
    );

    // --- 3. Prepare File Stream ---
    const fileBuffer = file.buffer;
    const readStream = new Readable({
      read() {
        this.push(fileBuffer);
        this.push(null); // Signal end of stream
      },
    });

    // --- 4. Call Azure Read API ---
    let resultText = '';
    try {
      this.logger.debug(`Calling Azure readInStream for user ${user.userId}`);
      const streamResponse = await computerVisionClient.readInStream(
        () => readStream,
      );

      const operationLocation = streamResponse.operationLocation;
      if (!operationLocation) {
        this.logger.error(
          'Failed to get operation location from Azure Vision API response.',
          streamResponse,
        );
        throw new ServiceUnavailableException(
          'Failed to initiate OCR process.',
        );
      }

      const operationId = operationLocation.substring(
        operationLocation.lastIndexOf('/') + 1,
      );
      this.logger.debug(
        `Azure OCR Operation ID: ${operationId} for user ${user.userId}`,
      );

      // --- 5. Poll for Results ---
      let pollCount = 0;
      const maxPolls = 60; // Poll for max 60 seconds

      while (pollCount < maxPolls) {
        const readOpResult =
          await computerVisionClient.getReadResult(operationId);
        this.logger.debug(
          `Polling OCR result status: ${readOpResult.status} (Poll ${pollCount + 1}/${maxPolls})`,
        );

        // Compare status using string literals based on SDK v8.2.0
        if (readOpResult.status === 'failed') {
          this.logger.error(
            `Azure OCR process failed for operation ID: ${operationId}`,
          );
          throw new ServiceUnavailableException('OCR processing failed.');
        }

        if (readOpResult.status === 'succeeded') {
          if (
            readOpResult.analyzeResult &&
            readOpResult.analyzeResult.readResults
          ) {
            resultText = readOpResult.analyzeResult.readResults
              .map((pageResult) =>
                pageResult.lines.map((line) => line.text).join('\n'),
              )
              .join('\n\n'); // Join pages with double newline
          } else {
            this.logger.warn(
              `OCR succeeded but analyzeResult or readResults were missing for operation ID: ${operationId}`,
            );
          }
          this.logger.log(
            `OCR process succeeded for operation ID: ${operationId}`,
          );
          break; // Exit loop on success
        }

        // Check for non-terminal states using string literals
        if (
          readOpResult.status !== 'running' &&
          readOpResult.status !== 'notStarted'
        ) {
          this.logger.warn(
            `OCR process ended with unexpected status: ${readOpResult.status} for operation ID: ${operationId}`,
          );
          throw new ServiceUnavailableException(
            `OCR processing ended unexpectedly with status: ${readOpResult.status}`,
          );
        }

        await sleep(1000); // Wait 1 second before next poll
        pollCount++;
      }

      if (pollCount >= maxPolls) {
        this.logger.error(
          `OCR process timed out after ${maxPolls} seconds for operation ID: ${operationId}`,
        );
        throw new ServiceUnavailableException('OCR processing timed out.');
      }
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown Azure Vision API error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Azure Vision API call failed for user ${user.userId}: ${message}`,
        stack,
      );
      // Re-throw specific exceptions if needed, otherwise wrap
      if (
        error instanceof ServiceUnavailableException ||
        error instanceof InternalServerErrorException ||
        error instanceof ForbiddenException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new ServiceUnavailableException(
        `Failed to communicate with OCR service: ${message}`,
      );
    }

    this.logger.log(`OCR completed successfully for user ${user.userId}`);
    return { success: true, result: resultText };
  }
  // Removed extra closing brace here

  // --- Methods moved from RecentModelsService ---

  async getRecentModels(userId: string, limit: number = 4) {

    try {
      const recentModels = await this.prisma.user_recent_model.findMany({
        where: { userId: userId },
        orderBy: { lastUsedAt: 'desc' },
        take: limit,
        include: {
          model: {
            // Include related model details
            select: {
              id: true,
              display_name: true,
              model_name: true,
              deployment_name: true,
              category: true, // Include the category
              // Add other fields needed by the frontend ModelCard
            },
          },
        },
      });


      // Map the result to return only the model details
      // Need to handle potential null model relation if data integrity allows it
      const mappedModels = recentModels
        .map((rm) => rm.model)
        .filter((model) => model !== null);


      return mappedModels;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `[getRecentModels] ERROR - Failed for user "${userId}": ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve recent models.',
      );
    }
  }

  async getRecentModelsWithTimestamp(userId: string, limit: number = 10) {
    this.logger.log(
      `[getRecentModelsWithTimestamp] START - Fetching recent models with timestamps for user "${userId}" with limit ${limit}`,
    );

    try {
      const recentModels = await this.prisma.user_recent_model.findMany({
        where: { userId: userId },
        orderBy: { lastUsedAt: 'desc' },
        take: limit,
        include: {
          model: {
            select: {
              id: true,
              display_name: true,
              model_name: true,
              deployment_name: true,
              category: true,
            },
          },
        },
      });

      // Map to include both model details and timestamp
      const modelsWithTimestamp = recentModels
        .filter((rm) => rm.model !== null)
        .map((rm) => ({
          ...rm.model,
          lastUsedAt: rm.lastUsedAt,
        }));

      this.logger.log(
        `[getRecentModelsWithTimestamp] COMPLETE - Returning ${modelsWithTimestamp.length} models with timestamps for user "${userId}"`,
      );

      return modelsWithTimestamp;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `[getRecentModelsWithTimestamp] ERROR - Failed for user "${userId}": ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve recent models with timestamps.',
      );
    }
  }

  async recordModelUsage(userId: string, modelId: number) {
    this.logger.log(
      `[recordModelUsage] START - Recording usage of model ${modelId} for user "${userId}"`,
    );

    try {
      // Validate modelId exists before upserting
      this.logger.debug(
        `[recordModelUsage] Checking if model ${modelId} exists in model_list table`,
      );

      const modelExists = await this.prisma.model_list.findUnique({
        where: { id: modelId },
      });

      if (!modelExists) {
        this.logger.warn(
          `[recordModelUsage] ABORT - Model ID ${modelId} not found in model_list table`,
        );
        return { success: false, error: 'Model not found.' };
      }

      this.logger.debug(
        `[recordModelUsage] Model ${modelId} exists: ${modelExists.model_name} (${modelExists.display_name})`,
      );

      // Log the upsert operation
      const now = getHongKongTime();
      this.logger.debug(
        `[recordModelUsage] Executing upsert: { userId: "${userId}", modelId: ${modelId}, lastUsedAt: "${now.toISOString()}" }`,
      );

      const result = await this.prisma.user_recent_model.upsert({
        where: {
          userId_modelId: {
            // Use the unique constraint name defined in the schema
            userId: userId,
            modelId: modelId,
          },
        },
        update: {
          lastUsedAt: now, // Update the timestamp
        },
        create: {
          userId: userId,
          modelId: modelId,
          // lastUsedAt is set by default on creation
        },
      });

      this.logger.log(
        `[recordModelUsage] SUCCESS - Recorded usage for model ${modelId} (${modelExists.model_name}), user "${userId}"`,
      );
      this.logger.debug(
        `[recordModelUsage] Upsert result:`,
        JSON.stringify(result, null, 2),
      );

      return { success: true };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `[recordModelUsage] ERROR - Failed for user "${userId}", model ${modelId}: ${message}`,
        stack,
      );

      // Log more details about the error
      if (
        error instanceof Error &&
        error.message.includes('Foreign key constraint')
      ) {
        this.logger.error(
          `[recordModelUsage] Possible foreign key issue - check if userId "${userId}" exists in acl_user table`,
        );
      }

      return { success: false, error: 'Failed to record model usage.' };
    }
  }

  // --- Method moved from ChatService ---

  async getConversationHistory(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedConversationHistoryResponseDto> {
    this.logger.log(
      `Fetching paginated conversation history for user ${userId}, page ${page}, limit ${limit} using SP.`,
    );

    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      this.logger.error('Encryption key/cert name not configured.');
      throw new InternalServerErrorException(
        'Server configuration error for encryption/decryption.',
      );
    }

    try {
      // Assumed SP result structure after modification (includes model_name)
      type SpResultItem = {
        conversation_uuid: string | null;
        create_dt: Date;
        model_name: string | null;
        conversation_title: string | null;
      };

      // Step 1: Get total items count for the user.
      // This is done separately as reliably getting OUTPUT param from SP with $queryRawUnsafe is complex.
      const totalItems = await this.prisma.conversation.count({
        where: { ssoid: userId },
      });

      if (totalItems === 0) {
        return {
          items: [],
          totalItems: 0,
          totalPages: 0,
          currentPage: page,
        };
      }

      const safeKeyName = encryptionKeyName.replace(/'/g, "''");
      const safeCertName = decryptionCertName.replace(/'/g, "''");

      // Step 2: Call the (modified) stored procedure with pagination parameters.
      // The SP `sp_cvst_GetConversationHistoryForUser` must be altered in the DB
      // to accept @PageNumber, @PageSize, use OFFSET/FETCH, and select model_name.
      const sql = `
        DECLARE @TotalRecordsOutput INT; -- Declare a variable for the OUTPUT parameter
        EXEC dbo.sp_cvst_GetConversationHistoryForUser
          @ssoid = '${userId}',
          @encryption_key_name = N'${safeKeyName}',
          @decryption_cert_name = N'${safeCertName}',
          @PageNumber = ${page},
          @PageSize = ${limit},
          @TotalRecords = @TotalRecordsOutput OUTPUT;
        -- The SP returns the items as its primary result set.
        -- The value of @TotalRecordsOutput is set by the SP but not directly selected back in this specific $queryRawUnsafe call,
        -- as we are fetching totalItems separately for simplicity with $queryRawUnsafe.
      `;
      // Note: The @TotalRecords OUTPUT parameter from the SP is now part of the EXEC call.
      // We are still using a separate COUNT query (line 565) for totalItems for robust retrieval with $queryRawUnsafe.

      const results: SpResultItem[] =
        await this.prisma.$queryRawUnsafe<SpResultItem[]>(sql);

      const itemsDto: ConversationHistoryItemDto[] = results.map((result) => {
        let title =
          result.conversation_title &&
          result.conversation_title !== 'Source Title is NULL' &&
          result.conversation_title !== 'Decryption/Conversion Failed'
            ? result.conversation_title
            : 'Untitled Chat';

        // Clean null characters from the title
        if (title) {
          title = title.replace(/\u0000/g, '');
        }

        // Truncate after cleaning
        title = title.substring(0, 50) + (title.length > 50 ? '...' : '');

        return {
          id: result.conversation_uuid || uuidv4(), // Fallback ID if null
          title: title,
          model: result.model_name, // SP must be modified to return this
          updated_at: result.create_dt.toISOString(), // Using create_dt as conversation's date
        };
      });

      const totalPages = Math.ceil(totalItems / limit);

      return {
        items: itemsDto,
        totalItems,
        totalPages,
        currentPage: page,
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error fetching paginated conversation history for user ${userId} via SP: ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve paginated conversation history via SP.',
      );
    }
  }

  /**
   * Updates the reaction for a specific message
   * @param messageId - UUID of the message to update
   * @param reaction - Reaction value: 1 (like), 0 (dislike), -1 (clear)
   * @param userId - User ID making the reaction
   */
  async updateMessageReaction(
    messageId: string,
    reaction: number,
    userId: string,
  ): Promise<void> {
    try {
      // Convert reaction number to database format
      let reactionValue: string | null;
      switch (reaction) {
        case 1:
          reactionValue = 'L'; // Like
          break;
        case 0:
          reactionValue = 'D'; // Dislike
          break;
        case -1:
          reactionValue = null; // Clear reaction
          break;
        default:
          throw new BadRequestException('Invalid reaction value');
      }

      this.logger.log(
        `Updating reaction for message ${messageId} by user ${userId} to ${reactionValue}`,
      );

      // Update the message reaction in database
      const result = await this.prisma.message.updateMany({
        where: {
          message_uuid: messageId,
          create_by: userId, // Ensure user can only update their own messages' reactions
        },
        data: {
          reaction: reactionValue,
          update_by: userId,
          update_dt: getHongKongTime(),
        },
      });

      if (result.count === 0) {
        throw new NotFoundException(
          'Message not found or user not authorized to update reaction',
        );
      }

      this.logger.log(
        `Successfully updated reaction for message ${messageId}`,
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      const message = error instanceof Error ? error.message : 'Unknown error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error updating reaction for message ${messageId}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException('Failed to update message reaction');
    }
  }
}
