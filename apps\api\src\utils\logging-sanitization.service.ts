import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class LoggingSanitizationService {
  private readonly logger = new Logger(LoggingSanitizationService.name);

  /**
   * Sanitizes messages for logging by replacing base64 image data with file path references
   * @param messages Array of messages to sanitize
   * @param base64ToFilePathMap Optional mapping from base64 keys to file paths
   * @returns Sanitized messages safe for logging
   */
  sanitizeMessagesForLogging(
    messages: any[],
    base64ToFilePathMap?: Map<string, string>
  ): any[] {
    if (!messages || !Array.isArray(messages)) {
      return messages;
    }

    const sanitizeContentArray = (content: any[]) => {
      return content.map(part => {
        if (part.type === 'image_url' && part.image_url?.url) {
          const url = part.image_url.url;
          if (url.startsWith('data:image/') && url.includes('base64,')) {
            // Extract base64 data for mapping lookup
            const base64Data = url.split('base64,')[1];
            const base64Key = base64Data?.substring(0, 50); // Use first 50 chars as key

            // Try to find corresponding file path
            const filePath = base64ToFilePathMap?.get(base64Key);
            const replacement = filePath
              ? `[IMAGE_FILE_PATH: ${filePath}]`
              : '[IMAGE_FILE_PATH: base64_data_replaced]';

            return {
              ...part,
              image_url: {
                ...part.image_url,
                url: replacement
              }
            };
          }
        }
        return part;
      });
    };

    const sanitizeTextWithBase64 = (text: string) => {
      if (typeof text !== 'string') return text;
      // Handle base64 URLs within JSON strings in text
      return text.replace(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g, (match) => {
        const base64Data = match.split('base64,')[1];
        const base64Key = base64Data?.substring(0, 50);
        
        const filePath = base64ToFilePathMap?.get(base64Key);
        return filePath 
          ? `[IMAGE_FILE_PATH: ${filePath}]`
          : '[IMAGE_FILE_PATH: base64_data_replaced]';
      });
    };

    return messages.map(msg => {
      let sanitizedMsg = { ...msg };

      // Handle Gemini message structure with parts array
      if (msg.parts && Array.isArray(msg.parts)) {
        sanitizedMsg.parts = msg.parts.map((part: any) => {
          if (part.text && typeof part.text === 'string') {
            return {
              ...part,
              text: sanitizeTextWithBase64(part.text)
            };
          }
          return part;
        });
      }

      // Handle LangChain message objects - sanitize BOTH lc_kwargs.content AND direct content
      if (msg.lc_kwargs && msg.lc_kwargs.content && Array.isArray(msg.lc_kwargs.content)) {
        sanitizedMsg.lc_kwargs = {
          ...msg.lc_kwargs,
          content: sanitizeContentArray(msg.lc_kwargs.content)
        };
      }

      // Also sanitize direct content property if it exists
      if (msg.content && Array.isArray(msg.content)) {
        sanitizedMsg.content = sanitizeContentArray(msg.content);
      }

      return sanitizedMsg;
    });
  }

  /**
   * Sanitizes any object containing messages or nested content for logging
   * @param obj Object to sanitize (e.g., payload, options, agentInput)
   * @param base64ToFilePathMap Optional mapping from base64 keys to file paths
   * @returns Sanitized object safe for logging
   */
  sanitizeObjectForLogging(
    obj: any,
    base64ToFilePathMap?: Map<string, string>
  ): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // Handle null
    if (obj === null) {
      return null;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObjectForLogging(item, base64ToFilePathMap));
    }

    // Handle objects
    const sanitizedObj = { ...obj };

    // Look for common message-containing properties
    const messageProperties = ['messages', 'chat_history', 'input'];
    
    for (const prop of messageProperties) {
      if (sanitizedObj[prop] && Array.isArray(sanitizedObj[prop])) {
        sanitizedObj[prop] = this.sanitizeMessagesForLogging(sanitizedObj[prop], base64ToFilePathMap);
      }
    }

    // Recursively sanitize nested objects (but avoid infinite loops)
    for (const [key, value] of Object.entries(sanitizedObj)) {
      if (typeof value === 'string') {
        // Apply string sanitization to all string values
        sanitizedObj[key] = this.sanitizeStringForLogging(value, base64ToFilePathMap);
      } else if (value && typeof value === 'object' && key !== 'lc_kwargs') {
        sanitizedObj[key] = this.sanitizeObjectForLogging(value, base64ToFilePathMap);
      }
    }

    return sanitizedObj;
  }

  /**
   * Sanitizes a string that might contain base64 image data
   * @param content String content to sanitize
   * @param base64ToFilePathMap Optional mapping from base64 keys to file paths
   * @returns Sanitized string safe for logging
   */
  sanitizeStringForLogging(
    content: string,
    base64ToFilePathMap?: Map<string, string>
  ): string {
    if (!content || typeof content !== 'string') {
      return content;
    }

    // Check if this is a JSON string that contains message parts
    let parsedContent: any = null;
    try {
      parsedContent = JSON.parse(content);
    } catch {
      // Not JSON, proceed with normal string sanitization
    }

    if (Array.isArray(parsedContent)) {
      // Handle JSON string containing message parts
      const sanitizedParts = parsedContent.map(part => {
        if (part.type === 'image_url' && part.image_url?.url) {
          const url = part.image_url.url;
          if (url.startsWith('data:image/') && url.includes('base64,')) {
            const base64Data = url.split('base64,')[1];
            const base64Key = base64Data?.substring(0, 50);
            
            const filePath = base64ToFilePathMap?.get(base64Key);
            const replacement = filePath 
              ? `[IMAGE_FILE_PATH: ${filePath}]`
              : '[IMAGE_FILE_PATH: base64_data_replaced]';

            return {
              ...part,
              image_url: {
                ...part.image_url,
                url: replacement
              }
            };
          }
        }
        return part;
      });
      return JSON.stringify(sanitizedParts);
    }

    // Look for data:image/... patterns and replace them in regular strings
    return content.replace(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g, (match) => {
      const base64Data = match.split('base64,')[1];
      const base64Key = base64Data?.substring(0, 50);
      
      const filePath = base64ToFilePathMap?.get(base64Key);
      return filePath 
        ? `[IMAGE_FILE_PATH: ${filePath}]`
        : '[IMAGE_FILE_PATH: base64_data_replaced]';
    });
  }
}