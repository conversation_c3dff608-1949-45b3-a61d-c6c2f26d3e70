'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import TuneIcon from '@mui/icons-material/Tune';
import IosShareIcon from '@mui/icons-material/IosShare';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import Tooltip from '@mui/material/Tooltip';
import MenuIcon from '@mui/icons-material/Menu';
import SettingsInputComponentIcon from '@mui/icons-material/SettingsInputComponent';
import { ClickAwayListener, TextField, InputAdornment } from '@mui/material';
import { useRenameConversationMutation } from '@/lib/store/apiSlice';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import { event } from '@/components/genai/GoogleAnalytics';
import { useChatLayout } from '@/contexts/ChatLayoutContext';
import { useAppSelector } from '@/lib/store/hooks';
import { selectShowTncModal, selectTncModalShowAgree } from '@/lib/store/uiSlice';

interface ChatHeaderProps {
  chatId: string | string[];
  chatTopic?: string;
  onOpenParameters: () => void;
  onShare?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  chatId,
  chatTopic = 'Chat Topic',
  onOpenParameters,
  onShare,
}) => {
  const router = useRouter();
  const { toggleSidebar } = useChatLayout();
  const [showMenu, setShowMenu] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(chatTopic);
  const [renameConversation] = useRenameConversationMutation();
  
  // Subscribe to T&C blocking state
  const showTncModal = useAppSelector(selectShowTncModal);
  const tncModalShowAgree = useAppSelector(selectTncModalShowAgree);
  const isTncBlocking = showTncModal && tncModalShowAgree;

  useEffect(() => {
    setEditedTitle(chatTopic);
  }, [chatTopic]);


  const handleShare = () => {
    closeMenu();
    if (onShare) {
      onShare();
      event({
        action: 'click',
        category: 'chat_header',
        label: 'share_conversation',
        value: 1,
      });
    } else {
      console.log('Share functionality not implemented');
    }
  };

  const handleParameters = () => {
    closeMenu();
    onOpenParameters();
    event({
      action: 'click',
      category: 'chat_header',
      label: 'open_parameters',
      value: 1,
    });
  };

  const toggleMenu = () => {
    setShowMenu((state) => !state);
  };

  const closeMenu = () => {
    setShowMenu((state) => (state == true ? false : state));
  };

  const handleTitleClick = () => {
    setIsEditing(true);
  };

  const handleConfirmRename = () => {
    const conversationId = Array.isArray(chatId) ? chatId[0] : chatId;
    renameConversation({
      conversationId,
      newTitle: editedTitle,
    });
    setIsEditing(false);
    event({
      action: 'click',
      category: 'chat_header',
      label: 'confirm_rename',
      value: 1,
    });
  };

  const handleCancelRename = () => {
    setEditedTitle(chatTopic);
    setIsEditing(false);
  };

  return (
    <Box
      sx={{
        position: 'relative',
        borderBottom: '1px solid',
        borderColor: 'divider',
        bgcolor: 'background.default',
        py: 0.5,
      }}
    >
      <Toolbar
        variant="dense"
        disableGutters
        sx={{ justifyContent: 'space-between', position: 'relative', px: 2 }}
      >
        <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
        {isEditing ? (
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <TextField
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleConfirmRename();
                } else if (e.key === 'Escape') {
                  handleCancelRename();
                }
              }}
              variant="standard"
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={handleConfirmRename}>
                      <CheckIcon />
                    </IconButton>
                    <IconButton size="small" onClick={handleCancelRename}>
                      <CloseIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        ) : (
          <Typography
            variant="h6"
            noWrap
            component="div"
            onClick={handleTitleClick}
            sx={{
              flex: 1,
              textAlign: 'center',
              fontWeight: 'medium',
              opacity: 0.7,
              cursor: 'pointer',
              fontSize: '1rem',
            }}
          >
            {chatTopic}
          </Typography>
        )}
        <IconButton
          onClick={toggleMenu}
          size="small"
          sx={{ display: { xs: undefined, md: 'none' } }}
        >
          <SettingsInputComponentIcon />
        </IconButton>

        {!isEditing && (
          <Box
            sx={{
              position: 'absolute',
              p: '0px 16px',
              right: 0,
              top: { xs: '100%', md: '50%' },
              transform: { xs: '', md: 'translateY(-50%)' },
              display: 'flex',
              gap: { xs: 1, md: 0.5 },
              flexDirection: { xs: 'column', md: 'row' },
              bgcolor: 'background.default',
              zIndex: 1200,
              height: { xs: showMenu ? 126 : 0, md: 'auto' },
              overflow: 'hidden',
              borderRadius: { xs: '0 0 10px 10px', md: 0 },
              boxShadow: {
                xs: showMenu ? '0px 2px 3px rgba(0,0,0,0.05)' : undefined,
                md: '0',
              },
              transition: { xs: 'all 0.3s ease', md: '' },
              justifyContent: 'start',
              alignItems: 'start',
              pointerEvents: isTncBlocking ? 'none' : 'auto',
              opacity: isTncBlocking ? 0.5 : 1,
            }}
          >
            <Tooltip
              title={onShare ? 'Share Conversation' : 'Share (coming soon)'}
            >
              <IconButton
                onClick={handleShare}
                size="small"
                disabled={!onShare}
                data-joyride="share-chat-button"
              >
                <IosShareIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Model Parameters">
              <IconButton
                onClick={handleParameters}
                size="small"
                data-joyride="model-parameters-button"
              >
                <TuneIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Toolbar>
    </Box>
  );
};

export default ChatHeader;
