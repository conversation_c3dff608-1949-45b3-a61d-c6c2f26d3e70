import React from 'react';

const AdobeFireflyLogo: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    width="20"
    height="20"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 34 32"
    fill="none"
    {...props} // Pass any additional props
  >
    <g clipPath="url(#clip0_9808_167338)">
      <path d="M27.7188 0H6.27884C3.0355 0 0.40625 2.62925 0.40625 5.87259V26.1274C0.40625 29.3708 3.0355 32 6.27884 32H27.7188C30.9622 32 33.5914 29.3708 33.5914 26.1274V5.87259C33.5914 2.62925 30.9622 0 27.7188 0Z" fill="#EB1000"/>
      <path d="M13.1866 22.2637C13.1866 22.3941 13.1273 22.4533 12.9969 22.4533H10.2888C10.1821 22.4533 10.1406 22.3763 10.1406 22.2637V8.26072C10.1406 8.13627 10.1821 8.08887 10.3066 8.08887H18.988C19.1362 8.08887 19.1717 8.13035 19.1954 8.27257L19.4384 10.637C19.4621 10.7674 19.3969 10.8266 19.2547 10.8266H13.1866V14.157H18.6562C18.7806 14.157 18.8399 14.1985 18.8399 14.3289V16.7229C18.8399 16.8533 18.7747 16.8948 18.668 16.8948H13.1866V22.2637Z" fill="white"/>
      <path d="M21.0625 11.7394C21.0625 11.6149 21.1218 11.5557 21.2462 11.5557H23.9425C24.0847 11.5557 24.1262 11.5912 24.1262 11.7334V22.246C24.1262 22.3883 24.0847 22.4475 23.9247 22.4475H21.2818C21.1336 22.4475 21.0625 22.3823 21.0625 22.2401V11.7334V11.7394Z" fill="white"/>
      <path d="M23.2563 9.18529L24.5955 9.48752C25.123 9.60604 25.4904 8.97789 25.123 8.58085L24.1926 7.57344C24.0681 7.44307 24.0207 7.25937 24.0622 7.08159L24.3644 5.74233C24.483 5.21492 23.8489 4.85344 23.4518 5.22085L22.4444 6.15122C22.3141 6.27566 22.1244 6.32307 21.9526 6.28159L20.6133 5.97937C20.0859 5.86085 19.7185 6.489 20.0859 6.88603L21.0163 7.89344C21.1407 8.02381 21.1881 8.20752 21.1467 8.38529L20.8444 9.72455C20.7259 10.252 21.36 10.6134 21.757 10.246L22.7644 9.31566C22.8948 9.19122 23.0844 9.14381 23.2563 9.18529Z" fill="white"/>
    </g>
    <defs>
      <clipPath id="clip0_9808_167338">
        <rect width="33.1852" height="32" fill="white" transform="translate(0.40625)"/>
      </clipPath>
    </defs>
  </svg>
);

export default AdobeFireflyLogo;
