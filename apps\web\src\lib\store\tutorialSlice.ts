import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store';

interface TutorialState {
  run: boolean;
  stepIndex: number;
  tour: string; // To identify which tour is active (e.g., 'main', 'chat', 'history')
}

const initialState: TutorialState = {
  run: false,
  stepIndex: 0,
  tour: 'main',
};

const tutorialSlice = createSlice({
  name: 'tutorial',
  initialState,
  reducers: {
    startTour: (state, action: PayloadAction<{ tour: string }>) => {
      state.tour = action.payload.tour;
      state.run = true;
      state.stepIndex = 0;
    },
    stopTour: (state) => {
      state.run = false;
      state.stepIndex = 0; // Reset step index for cleaner state
    },
    setStepIndex: (state, action: PayloadAction<number>) => {
      state.stepIndex = action.payload;
    },
    resetTour: (state) => {
      state.run = true;
      state.stepIndex = 0;
    },
  },
});

export const { startTour, stopTour, setStepIndex, resetTour } = tutorialSlice.actions;

export const selectTutorialRun = (state: RootState) => state.tutorial.run;
export const selectTutorialStepIndex = (state: RootState) => state.tutorial.stepIndex;
export const selectTutorialTour = (state: RootState) => state.tutorial.tour;

export default tutorialSlice.reducer;