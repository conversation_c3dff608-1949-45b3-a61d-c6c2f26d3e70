'use client';

import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import { useTheme, useMediaQuery } from '@mui/material';
import { usePathname } from 'next/navigation';
import { useSwipeable } from 'react-swipeable';

interface ChatLayoutContextProps {
  isSourcesPanelOpen: boolean;
  setIsSourcesPanelOpen: Dispatch<SetStateAction<boolean>>;
  toggleSourcesPanel: () => void;
  isSidebarExpanded: boolean;
  setIsSidebarExpanded: Dispatch<SetStateAction<boolean>>;
  toggleSidebar: () => void;
  openSidebarOnMobile: () => void;
  closeSidebarOnMobile: () => void;
  isNotFound: boolean;
  setIsNotFound: Dispatch<SetStateAction<boolean>>;
  markNotFound?: () => void;
}

const ChatLayoutContext = createContext<ChatLayoutContextProps | undefined>(
  undefined,
);

export const ChatLayoutProvider = ({ children }: { children: ReactNode }) => {
  const [isSourcesPanelOpen, setIsSourcesPanelOpen] = useState(false);
  const [isNotFound, setIsNotFound] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const pathname = usePathname();

  // Default to true on server, let effect handle client-side state
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);

  // Debug logging for sidebar state
  useEffect(() => {
    console.log('[ChatLayoutContext] State changed:', {
      pathname,
      isMobile,
      isSidebarExpanded,
      isNotFound,
      timestamp: new Date().toISOString()
    });
  }, [pathname, isMobile, isSidebarExpanded, isNotFound]);

  // This effect handles sidebar state on screen size changes only
  useEffect(() => {
    console.log('[ChatLayoutContext] Screen size changed:', { isMobile, currentExpanded: isSidebarExpanded });
    if (isMobile) {
      setIsSidebarExpanded(false);
      console.log('[ChatLayoutContext] Collapsed sidebar for mobile');
    } else {
      setIsSidebarExpanded(true);
      console.log('[ChatLayoutContext] Expanded sidebar for desktop');
    }
  }, [isMobile]); // Removed pathname dependency to preserve user preference across navigation

  const toggleSourcesPanel = () => {
    setIsSourcesPanelOpen((prev) => !prev);
  };

  const toggleSidebar = () => {
    setIsSidebarExpanded((prev) => !prev);
  };

  const openSidebarOnMobile = () => {
    if (isMobile) {
      setIsSidebarExpanded(true);
    }
  };

  const closeSidebarOnMobile = () => {
    if (isMobile) {
      setIsSidebarExpanded(false);
    }
  };

  const markNotFound = () => {
    setIsNotFound(true);
  };

  // Add keyboard shortcut for emergency sidebar toggle (Ctrl+B or Cmd+B)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        console.log('[ChatLayoutContext] Emergency sidebar toggle via keyboard');
        setIsSidebarExpanded(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <ChatLayoutContext.Provider
      value={{
        isSourcesPanelOpen,
        setIsSourcesPanelOpen,
        toggleSourcesPanel,
        isSidebarExpanded,
        setIsSidebarExpanded,
        toggleSidebar,
        openSidebarOnMobile,
        closeSidebarOnMobile,
        isNotFound,
        setIsNotFound,
        markNotFound,
      }}
    >
      {children}
    </ChatLayoutContext.Provider>
  );
};

export const useChatLayout = (): ChatLayoutContextProps => {
  const context = useContext(ChatLayoutContext);
  if (context === undefined) {
    throw new Error('useChatLayout must be used within a ChatLayoutProvider');
  }
  return context;
};
