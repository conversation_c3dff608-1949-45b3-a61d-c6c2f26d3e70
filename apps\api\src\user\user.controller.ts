import { Body, Controller, Get, Post, Req, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { UserService } from './user.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Request } from 'express';

@Controller('user')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('complete-tutorial')
  async completeTutorial(
    @Req() req: Request,
    @Body('tour') tour: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (req.user) {
        const userId = req.user['userId'];
        console.log(`[UserController] Starting completeTutorial for user: ${userId}, tour: ${tour}`);
        await this.userService.completeTutorial(userId, tour);
        console.log(`[UserController] Successfully completed tutorial for user: ${userId}, tour: ${tour}`);
        return { success: true, message: 'Tutorial marked as completed successfully' };
      } else {
        console.error('[UserController] No authenticated user found');
        throw new HttpException('User not authenticated', HttpStatus.UNAUTHORIZED);
      }
    } catch (error) {
      console.error(`[UserController] Error completing tutorial:`, error);
      
      // Type guard for Error objects
      const isError = error instanceof Error;
      const errorMessage = isError ? error.message : String(error);
      
      // Check if it's our specific user not found error
      if (errorMessage.includes('not found in acl_user_details table')) {
        throw new HttpException(
          {
            success: false,
            message: 'User account not found. Please contact an administrator.',
            error: errorMessage
          },
          HttpStatus.BAD_REQUEST
        );
      }
      
      // Generic error handling
      throw new HttpException(
        {
          success: false,
          message: 'Failed to mark tutorial as completed',
          error: errorMessage
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('reset-tutorial')
  async resetTutorial(@Req() req: Request): Promise<void> {
    if (req.user) {
      const userId = req.user['userId'];
      await this.userService.resetTutorial(userId);
    }
  }

  @Get('tutorial-status')
  async getTutorialStatus(
    @Req() req: Request,
  ): Promise<{ completed: boolean; completed_tours: string[] }> {
    if (req.user) {
      const userId = req.user['userId'];
      return this.userService.getTutorialStatus(userId);
    }
    return { completed: false, completed_tours: [] };
  }

  @Get('welcome-status')
  async getWelcomeStatus(
    @Req() req: Request,
  ): Promise<{ notified: boolean }> {
    if (req.user) {
      const userId = req.user['userId'];
      return this.userService.getWelcomeStatus(userId);
    }
    return { notified: false };
  }

  @Post('mark-welcome-seen')
  async markWelcomeSeen(@Req() req: Request): Promise<{ success: boolean }> {
    if (req.user) {
      const userId = req.user['userId'];
      return this.userService.markWelcomeSeen(userId);
    }
    return { success: false };
  }
}