'use client';

import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import FolderOutlinedIcon from '@mui/icons-material/FolderOutlined';
import ThumbUpAltOutlinedIcon from '@mui/icons-material/ThumbUpAltOutlined';
import ThumbDownAltOutlinedIcon from '@mui/icons-material/ThumbDownAltOutlined';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import FileCopyOutlinedIcon from '@mui/icons-material/FileCopyOutlined';
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined';
import PlayArrowOutlinedIcon from '@mui/icons-material/PlayArrowOutlined';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CloseIcon from '@mui/icons-material/Close';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined'; // Icon for file attachments
import RefreshIcon from '@mui/icons-material/Refresh'; // Icon for regenerate button
import Link from '@mui/material/Link';
import ListItemText from '@mui/material/ListItemText';
import ListItem from '@mui/material/ListItem';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { prism, darcula } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useTheme, styled } from '@mui/material/styles';
import { Conversation, Source } from '@/lib/store/chatSlice';
import CollapsibleContainer from '@/components/genai/ui/CollapsibleContainer';
import { AvatarGroup, CircularProgress } from '@mui/material';
import StreamingMarkdown from './StreamingMarkdown';

import { humanFileSize } from '@/lib/utils/HumanFileSize';
import { modelInfo } from '../model/ModelInfo';
import { copyToClipboard } from '@/lib/utils/copyUtils';
import { useUpdateMessageLikeMutation } from '@/lib/store/apiSlice';

interface ConversationDisplayProps {
  messages: Conversation[];
  onOpenSourcesPanel: (sources: Source[]) => void;
  onRegenerate?: () => void;
  isHistoryLoading?: boolean;
}

interface AttachmentData {
  name: string;
  type: string;
  size: number;
}

const StyledPre = styled('pre')(({ theme }) => ({
  margin: '0 !important',
  padding: '12px !important',
  backgroundColor: 'transparent !important',
  overflowX: 'auto',
  minWidth: '100%',
  '&::-webkit-scrollbar': {
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: theme.palette.grey[400],
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: theme.palette.grey[500],
  },
}));

const ConversationDisplay: React.FC<ConversationDisplayProps> = ({
  messages,
  onOpenSourcesPanel,
  onRegenerate,
  isHistoryLoading = false,
}) => {
  const [isRunModalOpen, setIsRunModalOpen] = useState(false);
  const [codeToRun, setCodeToRun] = useState('');
  const [languageToRun, setLanguageToRun] = useState('');
  const [copyStatus, setCopyStatus] = useState<{ [key: string]: 'idle' | 'copied' }>({});
  const [reactionLoading, setReactionLoading] = useState<{ [key: string]: boolean }>({});
  const [processedMessages, setProcessedMessages] = useState<{ [key: string]: { html: string; thinkingContent: string } }>({});
  const workerRef = useRef<Worker | null>(null);

  useEffect(() => {
    // Initialize the shared worker
    workerRef.current = new Worker(new URL('../../../workers/markdown.worker.ts', import.meta.url));

    workerRef.current.onmessage = (event: MessageEvent<{ html: string; thinkingContent: string; messageId: string }>) => {
      const { html, thinkingContent, messageId } = event.data;
      setProcessedMessages(prev => ({ ...prev, [messageId]: { html, thinkingContent } }));
    };

    return () => {
      workerRef.current?.terminate();
    };
  }, []);

  useEffect(() => {
    messages.forEach(message => {
      if (
        message.role === 'assistant' &&
        message.content &&
        message.messageId &&
        !processedMessages[message.messageId] &&
        !message.isStreaming
      ) {
        workerRef.current?.postMessage({
          content: message.content,
          sources: message.sources,
          messageId: message.messageId,
        });
      }
    });
  }, [messages, processedMessages]);
  
  // RTK Query mutation for updating message reactions
  const [updateMessageLike] = useUpdateMessageLikeMutation();

  const handleOpenSources = (sources: Source[] | undefined | Source) => {
    if (!sources) return;
    const sourcesArray = Array.isArray(sources) ? sources : [sources];
    if (sourcesArray.length > 0) {
      onOpenSourcesPanel(sourcesArray);
    }
  };

  const openRunModal = (code: string, language: string) => {
    setCodeToRun(code);
    setLanguageToRun(language);
    setIsRunModalOpen(true);
  };

  const closeRunModal = () => {
    setIsRunModalOpen(false);
    setCodeToRun('');
    setLanguageToRun('');
  };

  const handleCopyMessage = async (messageId: string, content: string) => {
    try {
      // Remove thinking blocks if present, but keep the rest of the markdown intact
      let cleanContent = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
      
      const success = await copyToClipboard(cleanContent);
      
      if (success) {
        setCopyStatus(prev => ({ ...prev, [messageId]: 'copied' }));
        setTimeout(() => {
          setCopyStatus(prev => ({ ...prev, [messageId]: 'idle' }));
        }, 2000);
      } else {
        console.error('Failed to copy message content');
      }
    } catch (error) {
      console.error('Copy message failed:', error);
    }
  };

  // Handle thumbs up reaction
  const handleThumbsUp = async (messageId: string | undefined, currentLike?: boolean) => {
    if (!messageId || isHistoryLoading || reactionLoading[messageId]) return;
    
    try {
      setReactionLoading(prev => ({ ...prev, [messageId]: true }));
      
      // If already liked, clear the reaction. Otherwise, set to liked.
      const newLikedState = currentLike === true ? undefined : true;
      
      await updateMessageLike({
        messageId,
        liked: newLikedState,
      }).unwrap();
      
      console.log(`Successfully updated reaction for message ${messageId} to: ${newLikedState}`);
    } catch (error) {
      console.error('Failed to update thumbs up reaction:', error);
    } finally {
      setReactionLoading(prev => ({ ...prev, [messageId]: false }));
    }
  };

  // Handle thumbs down reaction
  const handleThumbsDown = async (messageId: string | undefined, currentLike?: boolean) => {
    if (!messageId || isHistoryLoading || reactionLoading[messageId]) return;
    
    try {
      setReactionLoading(prev => ({ ...prev, [messageId]: true }));
      
      // If already disliked, clear the reaction. Otherwise, set to disliked.
      const newLikedState = currentLike === false ? undefined : false;
      
      await updateMessageLike({
        messageId,
        liked: newLikedState,
      }).unwrap();
      
      console.log(`Successfully updated reaction for message ${messageId} to: ${newLikedState}`);
    } catch (error) {
      console.error('Failed to update thumbs down reaction:', error);
    } finally {
      setReactionLoading(prev => ({ ...prev, [messageId]: false }));
    }
  };

  const CodeBlock = (
    props: {
      node?: any;
      inline?: boolean;
      className?: string;
      children?: React.ReactNode;
      role?: 'user' | 'assistant';
    } & React.HTMLAttributes<HTMLElement>,
  ) => {
    const { node, inline, className, children, role, ...restProps } = props;

    // Move hooks to top of component to avoid conditional calls
    const theme = useTheme();
    const [copyStatus, setCopyStatus] = useState<'idle' | 'copied'>('idle');

    if (role === 'user') {
      if (inline) {
        return (
          <code className={className} {...restProps}>
            {children}
          </code>
        );
      }
      return (
        <pre
          style={{
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all',
            margin: 0,
            fontFamily: 'monospace',
            backgroundColor: 'rgba(0,0,0,0.05)',
            padding: '8px',
            borderRadius: '4px',
          }}
        >
          <code className={className} {...restProps}>
            {children}
          </code>
        </pre>
      );
    }

    // Assistant role logic
    const match = /language-(\w+)/.exec(className || '');
    const language = match ? match[1] : 'text';

    const getNodeText = (n: any): string => {
      if (n.type === 'text') {
        return n.value || '';
      }
      if (n.children && Array.isArray(n.children)) {
        return n.children.map(getNodeText).join('');
      }
      return '';
    };

    const codeString = (node ? getNodeText(node) : String(children)).replace(
      /\n$/,
      '',
    );

    const handleCopy = () => {
      navigator.clipboard.writeText(codeString).then(
        () => {
          setCopyStatus('copied');
          setTimeout(() => setCopyStatus('idle'), 2000);
        },
        (err) => {
          console.error('Failed to copy code: ', err);
        },
      );
    };

    const handleDownload = () => {
      const fileExtensionMap: { [key:string]: string } = {
        javascript: 'js',
        python: 'py',
        html: 'html',
        css: 'css',
        json: 'json',
        typescript: 'ts',
        jsx: 'jsx',
        tsx: 'tsx',
        bash: 'sh',
      };
      const extension = fileExtensionMap[language] || 'txt';
      const filename = `snippet.${extension}`;
      const blob = new Blob([codeString], {
        type: `text/${extension === 'txt' ? 'plain' : language};charset=utf-8`,
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    };

    const handleRun = () => {
      openRunModal(codeString, language);
    };

    const showRunButton = ['html', 'javascript'].includes(language);

    if (inline) {
      return (
        <code className={className} {...restProps}>
          {children}
        </code>
      );
    }

    return (
      <Box
        sx={{
          my: 1,
          backgroundColor: 'background.paper',
          borderRadius: '4px',
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            px: 1.5,
            py: 0.5,
            borderBottom: '1px solid',
            borderColor: 'divider',
            backgroundColor: 'action.hover',
          }}
        >
          <Typography
            variant="caption"
            sx={{ color: 'text.secondary', fontWeight: 500 }}
          >
            {language}
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <IconButton
              size="small"
              onClick={handleCopy}
              title="Copy code"
              sx={{ color: 'text.secondary' }}
            >
              {copyStatus === 'copied' ? (
                <CheckCircleOutlineIcon fontSize="inherit" color="success" />
              ) : (
                <FileCopyOutlinedIcon fontSize="inherit" />
              )}
            </IconButton>
            <IconButton
              size="small"
              onClick={handleDownload}
              title="Download snippet"
              sx={{ color: 'text.secondary' }}
            >
              <DownloadOutlinedIcon fontSize="inherit" />
            </IconButton>
            {showRunButton && (
              <IconButton
                size="small"
                onClick={handleRun}
                title="Run code"
                sx={{ color: 'text.secondary' }}
              >
                <PlayArrowOutlinedIcon fontSize="inherit" />
              </IconButton>
            )}
          </Box>
        </Box>
        <SyntaxHighlighter
          style={theme.palette.mode === 'dark' ? (darcula as any) : (prism as any)}
          language={language}
          PreTag={StyledPre}
          {...restProps}
        >
          {codeString}
        </SyntaxHighlighter>
      </Box>
    );
  };

  const getIframeContent = () => {
    if (languageToRun === 'html') {
      return codeToRun;
    } else if (languageToRun === 'javascript') {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <title>JS Runner</title>
          <style>body { font-family: sans-serif; padding: 10px; }</style>
        </head>
        <body>
          <h1>JavaScript Output</h1>
          <p><i>Note: This runs in a sandboxed iframe. Direct DOM manipulation of the parent page is not possible. Check the browser console for errors or logs.</i></p>
          <pre id="output" style="background-color: #eee; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;"></pre>
          <script>
            const outputElement = document.getElementById('output');
            const originalLog = console.log;
            console.log = (...args) => {
              originalLog.apply(console, args);
              outputElement.textContent += args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' ') + '\\n';
            };
            window.onerror = (message, source, lineno, colno, error) => {
              outputElement.textContent += \`ERROR: \${message} (Line: \${lineno})\n\`;
            };
            try {
              ${codeToRun}
            } catch (e) {
              console.error(e);
            }
          </script>
        </body>
        </html>
      `;
    }
    return '<p>Running this language is not supported in the modal.</p>';
  };

  const createLinkRenderer = (sources: Source[] | undefined) => {
    const LinkRenderer = (
      props: {
        href?: string;
        children?: React.ReactNode;
      } & React.HTMLAttributes<HTMLAnchorElement>,
    ) => {
      const { href, children, ...rest } = props;
      const textContent = React.Children.toArray(children).join('');

      const isCitation = /^\d+$/.test(textContent.trim());

      if (isCitation && sources) {
        const source = sources.find((s) => s.link === href);

        if (source) {
          return (
            <Chip
              label={textContent}
              size="small"
              clickable
              onClick={(e) => {
                e.preventDefault();
                handleOpenSources([source]);
              }}
              sx={{
                height: '20px',
                fontSize: '0.8rem',
                backgroundColor: 'action.hover',
                color: 'text.secondary',
                '&:hover': {
                  backgroundColor: 'action.selected',
                },
                cursor: 'pointer',
                verticalAlign: 'middle',
                m: '0 2px',
                textDecoration: 'none',
              }}
              component="a"
              href={href}
              target="_blank"
              rel="noopener noreferrer"
            />
          );
        }
      }

      return (
        <Link href={href} {...rest} target="_blank" rel="noopener noreferrer">
          {children}
        </Link>
      );
    };
    return LinkRenderer;
  };


  return (
    <>
      <List sx={{ width: '100%', p: 0, px: 2, py: 2 }}>
        {(() => {
          if (messages.length === 0) {
            return (
              <Typography
                sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}
              >
                Start the conversation by sending a message.
              </Typography>
            );
          } else {
            return messages.map((message, index) => {
              if (message.thinking && !message.content) {
                // Only show spinner if there's no content yet
                return (
                  <Box
                    key={`thinking-${index}`}
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      mb: 3,
                      px: 1,
                    }}
                  >
                    <CircularProgress color="primary" />
                  </Box>
                );
              }
              
              // Parse content for thinking blocks - moved back to regular processing to avoid hook violations
              const isUser = message.role === 'user';
              const isStreaming = message.isStreaming;
              const processed = !isUser && !isStreaming && message.messageId ? processedMessages[message.messageId] : null;

              const regularContent = message.content || (processed && processed.html);
              const thinkingContent = processed ? processed.thinkingContent : '';
              const hasThinkingBlock = !!thinkingContent;
              let attachments: AttachmentData[] | undefined = message.attachments;

              return (
                <Box
                  key={message.messageId || index}
                  sx={{
                    display: 'flex',
                    justifyContent:
                      message.role === 'user' ? 'flex-end' : 'flex-start',
                    mb: 3,
                    px: 1,
                  }}
                >
                  <Stack
                    direction="column"
                    alignItems={
                      message.role === 'user' ? 'flex-end' : 'flex-start'
                    }
                    sx={{ maxWidth: { xs: '95%', md: '75%' } }}
                  >
                    {message.role === 'user' &&
                      (attachments && attachments.length > 0) && (
                        <Stack spacing={1} sx={{ mb: 0.5, width: '100%' }}>
                          {attachments.map(
                            (file: AttachmentData, fileIndex: number) => (
                              <Paper
                                key={fileIndex}
                                elevation={0}
                                sx={{
                                  p: 1,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                  bgcolor: 'background.paper',
                                  borderRadius: '8px',
                                  border: '1px solid',
                                  borderColor: 'divider',
                                }}
                              >
                                <InsertDriveFileOutlinedIcon
                                  sx={{ color: 'text.secondary' }}
                                />
                                <Box sx={{ overflow: 'hidden' }}>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: 'text.primary',
                                      fontWeight: 500,
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                    }}
                                  >
                                    {file.name}
                                  </Typography>
                                </Box>
                              </Paper>
                            ),
                          )}
                        </Stack>
                      )}
                    {message.role === 'assistant' &&
                      hasThinkingBlock &&
                      thinkingContent && (
                        <Box
                          sx={{
                            width: '100%',
                            mb: 1,
                          }}
                        >
                          <CollapsibleContainer
                            title="Thoughts"
                            subtextCollapsed="Expand to show model's summarized thoughts"
                            subtextExpanded="Collapse to hide model's summarized thoughts"
                          >
                            <Box
                              sx={{
                                p: 1.5,
                                maxHeight: '200px',
                                overflowY: 'auto',
                                backgroundColor: 'action.hover',
                                borderRadius: '4px',
                                fontSize: '0.875rem',
                                color: 'text.secondary',
                                '&::-webkit-scrollbar': {
                                  width: '8px',
                                },
                                '&::-webkit-scrollbar-track': {
                                  backgroundColor: 'transparent',
                                },
                                '&::-webkit-scrollbar-thumb': {
                                  backgroundColor: (theme) =>
                                    theme.palette.grey[400],
                                  borderRadius: '4px',
                                },
                                '&::-webkit-scrollbar-thumb:hover': {
                                  backgroundColor: (theme) =>
                                    theme.palette.grey[500],
                                },
                              }}
                            >
                              <div dangerouslySetInnerHTML={{ __html: thinkingContent }} />
                            </Box>
                          </CollapsibleContainer>
                        </Box>
                      )}
                    {(regularContent ||
                      (message.role === 'assistant' && hasThinkingBlock)) && (
                      <Box
                        sx={{
                          maxWidth: '100%',
                          bgcolor:
                            message.role === 'user'
                               ? 'primary.main'
                               : 'transparent',
                          color:
                            message.role === 'user'
                               ? 'primary.contrastText'
                               : 'text.primary',
                          borderRadius:
                            message.role === 'user'
                               ? '16px 16px 0 16px'
                               : '16px 16px 16px 0',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                          wordBreak: 'break-word',
                          width: '100%',
                        }}
                      >
                        {message.role === 'assistant' && (
                          <Stack
                            direction="column"
                            spacing={1.5}
                            alignItems="start"
                            sx={{ mb: 1 }}
                          >
                            {message.sources &&
                              message.sources.length > 0 &&
                              (() => {
                                const sourcesCount = message.sources.length;
                                const remainingSources = sourcesCount - 3;
                                const sourcesText =
                                  remainingSources > 0
                                    ? `+${remainingSources} more`
                                    : `${sourcesCount} source${sourcesCount > 1 ? 's' : ''}`;

                                return (
                                  <Button
                                    variant="text"
                                    size="small"
                                    onClick={() =>
                                      handleOpenSources(message.sources)
                                    }
                                    startIcon={
                                      <AvatarGroup
                                        sx={{
                                          '& .MuiAvatar-root': {
                                            width: 20,
                                            height: 20,
                                            border: '2px solid #fff',
                                            fontSize: '0.65rem',
                                            boxShadow:
                                              '0 1px 2px rgba(0,0,0,0.1)',
                                          },
                                          '& .MuiAvatarGroup-avatar': {
                                            width: 20,
                                            height: 20,
                                            fontSize: '0.65rem',
                                            bgcolor: 'rgba(0, 0, 0, 0.12)',
                                            color: 'text.secondary',
                                          },
                                          flexDirection: 'row-reverse',
                                          ml: '-4px',
                                        }}
                                      >
                                        {message.sources
                                          .slice(0, 3)
                                          .map((source, index) => {
                                            try {
                                              const url = new URL(source.link);
                                              const faviconUrl = `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=32`;
                                              return (
                                                <Avatar
                                                  key={index}
                                                  src={faviconUrl}
                                                  sx={{
                                                    bgcolor: 'background.paper',
                                                  }}
                                                >
                                                  <FolderOutlinedIcon
                                                    sx={{
                                                      fontSize: '14px',
                                                      color: 'text.secondary',
                                                    }}
                                                  />
                                                </Avatar>
                                              );
                                            } catch (e) {
                                              return (
                                                <Avatar
                                                  key={index}
                                                  sx={{
                                                    bgcolor: 'background.paper',
                                                  }}
                                                >
                                                  <FolderOutlinedIcon
                                                    sx={{
                                                      fontSize: '14px',
                                                      color: 'text.secondary',
                                                    }}
                                                  />
                                                </Avatar>
                                              );
                                            }
                                          })}
                                      </AvatarGroup>
                                    }
                                    sx={{
                                      bgcolor: 'action.hover',
                                      color: 'text.secondary',
                                      borderRadius: '16px',
                                      textTransform: 'none',
                                      px: 1.5,
                                      py: 0.5,
                                      fontSize: '0.75rem',
                                      fontWeight: 500,
                                      border: '1px solid',
                                      borderColor: 'divider',
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        bgcolor: 'action.selected',
                                        borderColor: 'divider',
                                        transform: 'translateY(-1px)',
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                      },
                                      '& .MuiButton-startIcon': {
                                        marginRight: '6px',
                                        marginLeft: '-2px',
                                      },
                                    }}
                                  >
                                    {sourcesText}
                                  </Button>
                                );
                              })()}
                            {message.model_display_name && (
                              <Stack
                                direction="row"
                                spacing={0.5}
                                alignItems="center"
                              >
                                {!!message.model_display_name &&
                                  !!modelInfo[message.model_display_name] && (
                                    <Box
                                      sx={{
                                        '& svg': { width: 32, height: 32 },
                                      }}
                                    >
                                      {
                                        modelInfo[message.model_display_name]
                                          ?.svg
                                      }
                                    </Box>
                                  )}
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{ fontWeight: 500, fontSize: 15 }}
                                >
                                  {message.model_display_name}
                                </Typography>
                              </Stack>
                            )}
                          </Stack>
                        )}
                        <Box
                          sx={{
                            px: message.role === 'user' ? 2 : 0,
                            py: message.role === 'user' ? 0.5 : 0,
                            maxWidth: '100%',
                            overflowX: 'hidden', // Prevent horizontal scrolling
                            '& p, & > div': { marginY: '8px' },
                            '& h1, & h2, & h3, & h4, & h5, & h6': {
                              marginTop: '16px',
                              marginBottom: '8px',
                              fontWeight: 600,
                            },
                            '& li': { marginBottom: '4px' },
                            '& code:not(pre > code)': {
                              backgroundColor: 'action.hover',
                              padding: '0.2em 0.4em',
                              borderRadius: '3px',
                              fontSize: '85%',
                            },
                            '& table': {
                              borderCollapse: 'collapse',
                              width: '100%',
                              marginY: '8px',
                            },
                            '& th, & td': {
                              border: '1px solid',
                              borderColor: 'divider',
                              padding: '8px',
                              textAlign: 'left',
                            },
                            '& th': { backgroundColor: 'action.hover' },
                            '& a': {
                              color: 'primary.main',
                              textDecoration: 'underline',
                              wordBreak: 'break-all', // Force break to prevent overflow
                            },
                            '& .katex-display': {
                              overflowX: 'auto',
                              overflowY: 'hidden',
                              padding: '0.5em 0',
                            },
                          }}
                        >
                          {message.role === 'user' ? (
                            <Typography sx={{ whiteSpace: 'pre-wrap' }}>
                              {message.usedMention && message.model_display_name && (
                                <Typography
                                  component="span"
                                  sx={{
                                    fontWeight: 'bold',
                                    color: 'text.primary',
                                    mr: 1,
                                  }}
                                >
                                  @{message.model_display_name}
                                </Typography>
                              )}
                              {message.content}
                            </Typography>
                          ) : processed ? (
                            <Box dangerouslySetInnerHTML={{ __html: processed.html }} />
                          ) : (
                            <StreamingMarkdown content={message.content || ''} />
                          )}
                        </Box>
                      </Box>
                    )}
                    {message.role === 'assistant' &&
                      message.isStreaming && ( // Show streaming indicator
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <CircularProgress size={16} sx={{ mr: 1 }} />
                          <Typography variant="caption" color="text.secondary">
                            Generating response...
                          </Typography>
                        </Box>
                      )}
                    {message.role === 'assistant' &&
                      !message.isStreaming && ( // Action buttons remain at the bottom of the entire message stack item
                        <Stack direction="row" spacing={0.5} sx={{ mt: 0.5 }}>
                          <IconButton
                            size="small"
                            aria-label="thumbs up"
                            onClick={() => handleThumbsUp(message.messageId, message.like)}
                            disabled={isHistoryLoading || Boolean(message.messageId && reactionLoading[message.messageId])}
                            sx={{ 
                              color: message.like === true ? 'primary.main' : 'text.secondary',
                              '&:hover': {
                                color: message.like === true ? 'primary.dark' : 'primary.main',
                              }
                            }}
                          >
                            {message.messageId && reactionLoading[message.messageId] ? (
                              <CircularProgress size={16} />
                            ) : message.like === true ? (
                              <ThumbUpIcon fontSize="inherit" />
                            ) : (
                              <ThumbUpAltOutlinedIcon fontSize="inherit" />
                            )}
                          </IconButton>
                          <IconButton
                            size="small"
                            aria-label="thumbs down"
                            onClick={() => handleThumbsDown(message.messageId, message.like)}
                            disabled={isHistoryLoading || Boolean(message.messageId && reactionLoading[message.messageId])}
                            sx={{ 
                              color: message.like === false ? 'error.main' : 'text.secondary',
                              '&:hover': {
                                color: message.like === false ? 'error.dark' : 'error.main',
                              }
                            }}
                          >
                            {message.messageId && reactionLoading[message.messageId] ? (
                              <CircularProgress size={16} />
                            ) : message.like === false ? (
                              <ThumbDownIcon fontSize="inherit" />
                            ) : (
                              <ThumbDownAltOutlinedIcon fontSize="inherit" />
                            )}
                          </IconButton>
                          <IconButton
                            size="small"
                            aria-label="copy message"
                            title="Copy message"
                            onClick={() => handleCopyMessage(
                              message.messageId || `msg-${index}`,
                              message.content || ''
                            )}
                            sx={{ color: 'text.secondary' }}
                          >
                            {copyStatus[message.messageId || `msg-${index}`] === 'copied' ? (
                              <CheckCircleOutlineIcon fontSize="inherit" color="success" />
                            ) : (
                              <ContentCopyIcon fontSize="inherit" />
                            )}
                          </IconButton>
                          {/* Show regenerate button only for the last assistant message */}
                          {onRegenerate && 
                           index === messages.length - 1 && 
                           messages[messages.length - 2]?.role === 'user' && (
                            <IconButton
                              size="small"
                              aria-label="regenerate response"
                              title="Regenerate response"
                              onClick={onRegenerate}
                              sx={{ color: 'text.secondary' }}
                            >
                              <RefreshIcon fontSize="inherit" />
                            </IconButton>
                          )}
                        </Stack>
                      )}
                  </Stack>
                </Box>
              );
            });
          }
        })()}
      </List>
      {/* Run Code Modal */}
      <Dialog
        open={isRunModalOpen}
        onClose={closeRunModal}
        fullWidth
        maxWidth="md" // Adjust size as needed
      >
        <DialogTitle sx={{ m: 0, p: 2 }}>
          Run Code Snippet ({languageToRun})
          <IconButton
            aria-label="close"
            onClick={closeRunModal}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ p: 0 }}>
          {' '}
          {/* Remove padding for iframe */}
          <iframe
            srcDoc={getIframeContent()}
            title={`Run ${languageToRun} Code`}
            sandbox="allow-scripts allow-modals" // Basic sandbox, adjust as needed
            style={{ width: '100%', height: '60vh', border: 'none' }} // Adjust height
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={closeRunModal}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ConversationDisplay;
