import React, { useMemo } from 'react';
import { marked } from 'marked';
import hljs from 'highlight.js';
import { Box } from '@mui/material';
import { convertBracketedLatexToMarkdownMath } from '@/lib/utils/convertBracketedLatexToMarkdownMath';
import { parseThinkBlocks } from '@/lib/utils/markdownUtils';
import CollapsibleContainer from '@/components/genai/ui/CollapsibleContainer';

const renderer = new marked.Renderer();
renderer.code = ({ text: code, lang }: { text: string; lang?: string }) => {
  const language = lang && hljs.getLanguage(lang) ? lang : 'plaintext';
  const highlighted = hljs.highlight(code, { language, ignoreIllegals: true }).value;
  return `<pre><code class="hljs language-${language}">${highlighted}</code></pre>`;
};

marked.setOptions({
  renderer,
  gfm: true,
  breaks: true,
});

const StreamingMarkdown = ({ content }: { content: string }) => {
  const processedContent = useMemo(() => {
    const convertedContent = convertBracketedLatexToMarkdownMath(content);
    // Only extract regular content during streaming, ignore thinking blocks
    const { regularContent } = parseThinkBlocks(convertedContent);
    return marked.parse(regularContent) as string;
  }, [content]);

  return (
    <Box>
      <Box dangerouslySetInnerHTML={{ __html: processedContent }} />
    </Box>
  );
};

export default StreamingMarkdown;