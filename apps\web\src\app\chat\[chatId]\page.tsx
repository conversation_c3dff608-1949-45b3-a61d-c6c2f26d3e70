'use client';

import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  KeyboardEvent,
  FormEvent,
  MouseEvent,
} from 'react'; // useRef, useEffect already imported
import { useParams, useRouter } from 'next/navigation';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper'; // For side panel background
import IconButton from '@mui/material/IconButton'; // For close button
import CloseIcon from '@mui/icons-material/Close'; // For close button icon
import List from '@mui/material/List'; // For sources list
import ListItem from '@mui/material/ListItem'; // For sources list
import ListItemText from '@mui/material/ListItemText'; // For sources list
import Link from '@mui/material/Link'; // For source links
import { useSelector } from 'react-redux';

// Import the RTK Query hook and Redux hooks/selectors
import {
  useChatCompletionMutation,
  ChatCompletionRequest,
  useGetHistoryMessagesQuery,
  useShareConversationMutation,
  useUpdateConversationParamsMutation,
  useRewritePromptMutation,
  useGeneratePromptMutation,
} from '@/lib/store/apiSlice';
import { useAppSelector, useAppDispatch, useStreamingStatus } from '@/lib/store/hooks';
// Import Conversation type and relevant actions/selectors
import {
  Conversation,
  Source,
  selectMessages,
  selectIsThinking,
  addUserMessage,
  triggerClearMessage,
  setChatErrorMessage,
  setIsThinking,
  setConversationId,
  selectConversationId,
  setChatMessages,
  setSelectedNextModelName,
  selectStreamJustCompleted,
  setStreamJustCompleted,
  prepareForRegeneration,
  selectPastMessagesCount,
  selectInstructions,
  selectConversationModel,
  setActiveStreamingConversation,
  selectActiveStreamingConversation,
  selectPendingParameters,
  clearPendingParameters,
  switchConversationContext,
} from '@/lib/store/chatSlice'; // Added Source type + setSelectedNextModelName + selectStreamJustCompleted + setStreamJustCompleted + prepareForRegeneration + selectPastMessagesCount + selectConversationModel
// Import model selector
import { selectCurrentGptModel } from '@/lib/store/modelSlice';
// Import app selectors if needed for file lists
import { selectSupportedFileList } from '@/lib/store/appSlice';
import { selectAvailableModels } from '@/lib/store/modelSlice';
import type { GptModel } from '@/lib/types/common'; // Import GptModel type

import ChatHeader from '@/components/genai/chat/ChatHeader';
import ConversationDisplay from '@/components/genai/chat/ConversationDisplay';
import ChatInputArea from '@/components/genai/chat/ChatInputArea';
import ChatErrorDisplay from '@/components/genai/chat/ChatErrorDisplay'; // Import the new error display component
import ModelParametersModal from '@/components/genai/modals/ModelParametersModal'; // Import the new modal
import { ShareConversationModal } from '@/components/genai/modals/ShareConversationModal'; // Import share modal
import PromptGalleryModal from '@/components/genai/modals/PromptGalleryModal';
import PromptRewriteAssistantModal from '@/components/genai/modals/PromptRewriteAssistantModal'; // Import the new modal
import { v4 as uuidv4 } from 'uuid';
import { DialogTitle, Toolbar, useMediaQuery, useTheme } from '@mui/material';
import { stripAllModelMentions } from '@/lib/utils/mentionUtils'; // Import mention utility
import ModalContainer from '@/components/genai/modals/ModalContainer';

// Helper function to read file as base64
const readFileAsBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      // Remove the data URL prefix (e.g., "data:image/png;base64,")
      resolve(base64String.split(',')[1]);
    };
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

export default function ExistingChatIdPage() {
  const theme = useTheme(); // Access theme
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const params = useParams();
  const chatId = params?.chatId;

  // Use Redux store for messages and thinking state
  const messages = useAppSelector(selectMessages);
  const isAiTyping = useAppSelector(selectIsThinking);
  const streamJustCompleted = useAppSelector(selectStreamJustCompleted);
  // const currentGptModel = useAppSelector(selectCurrentGptModel); // Don't use this for submission
  const selectedNextModelName = useAppSelector(
    (state) => state.chat.selectedNextModelName,
  ); // Get the intended model name
  const conversationModel = useAppSelector(selectConversationModel); // Get the conversation's default model
  const dispatch = useAppDispatch();
  const supportedFileList = useAppSelector(selectSupportedFileList);
  const modelList = useAppSelector(selectAvailableModels);
  const visionModelList = modelList?.map((m: GptModel) => m.model_name) || []; // Add type annotation for m

  // Local state for input area
  const [tempFileList, setTempFileList] = useState<File[]>([]);
  const [useGoogleEnabled, setUseGoogleEnabled] = useState<boolean>(false);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const [promptContent, setPromptContent] = useState('');
  // const [systemInstruction, setSystemInstruction] = useState(''); // New state for system instruction
  const systemInstruction = useAppSelector(selectInstructions);
  // RTK Query Mutation hook
  const [updateParams, { isLoading, isError, error }] =
    useUpdateConversationParamsMutation();
  const setSystemInstruction = useCallback(async (instructions: string) => {
    const chat_session_id =
      (Array.isArray(chatId) ? chatId[0] : chatId) || undefined;
    if (chat_session_id) {
      await updateParams({
        chat_session_id,
        instructions,
      }).unwrap();
    }
  }, [chatId, updateParams]);

  const tempFileListRef = useRef<File[]>(tempFileList); // Ref to hold latest file list
  const scrollContainerRef = useRef<HTMLDivElement>(null); // Ref for the scrollable Box

  // State and ref for selected model mentions
  const [selectedModelMentions, setSelectedModelMentions] = useState<
    Map<string, number[]>
  >(new Map());
  const clearSelectedMentionsRef = useRef<(() => void) | undefined>(undefined);

  // State for sources side panel
  const [isSourcesPanelOpen, setIsSourcesPanelOpen] = useState(false);
  const [panelSources, setPanelSources] = useState<Source[] | undefined>(
    undefined,
  );

  // State for Model Parameters Modal
  const [isParamsModalOpen, setIsParamsModalOpen] = useState(false);

  // State for Share Modal
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isPromptGalleryOpen, setIsPromptGalleryOpen] = useState(false);
  // State for Prompt Rewrite Assistant Modal
  const [isPromptAssistantOpen, setIsPromptAssistantOpen] = useState(false);
  const [promptAssistantInitialContent, setPromptAssistantInitialContent] =
    useState('');

  // Use the RTK Query mutation hook for chat completion
  const [triggerChatCompletion, { isLoading: isSubmitting }] =
    useChatCompletionMutation();
  const [shareConversation] = useShareConversationMutation();
  const [updateConversationParams] = useUpdateConversationParamsMutation();
  const [rewritePrompt] = useRewritePromptMutation();
  const [generatePrompt] = useGeneratePromptMutation();

  const router = useRouter();
  const reduxConversationId = useSelector(selectConversationId);
  const pendingParameters = useSelector(selectPendingParameters);
  
  // Extract current chat ID from URL parameter
  const currentChatId = Array.isArray(chatId) ? chatId[0] : chatId;

  // Track previous conversation ID to detect switching
  const prevConversationIdRef = useRef<string | undefined>(reduxConversationId);
  // Track recently active conversations to force refetch
  const recentlyActiveChatsRef = useRef<Set<string>>(new Set());
  
  // AbortController for managing streaming requests
  const abortControllerRef = useRef<AbortController | null>(null);
  const { activeStreamingConversationId, isStreamingActiveForThisConversation } = useStreamingStatus();
  
  // Ref to track previous streaming status for refetch logic
  const prevStreamingStatusRef = useRef(isStreamingActiveForThisConversation);

  // --- Fetch History ---
  const {
    data: historyData,
    isLoading: isHistoryLoading,
    isError: isHistoryError,
    error: historyErrorData,
    isSuccess: isHistorySuccess,
    refetch,
  } = useGetHistoryMessagesQuery(reduxConversationId!, {
    skip:
      !reduxConversationId ||
      !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
        reduxConversationId,
      ) ||
      !modelList || modelList.length === 0 || // Wait for models to be loaded for better display name resolution
      isStreamingActiveForThisConversation,          // prevents dispatch
    refetchOnMountOrArgChange: !isStreamingActiveForThisConversation, // Force refetch when conversation ID changes
  });

  const historyMessages = historyData?.messages;
  const conversationTitle = historyData?.title;

  // console.log('[ExistingChatIdPage] historyData:', historyData); // Keep for debugging history if needed
  // console.log('[ExistingChatIdPage] conversationTitle:', conversationTitle); // Keep for debugging history if needed

  // Effect to set conversationId in Redux based on URL parameter
  useEffect(() => {
    const uuidRegex =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

    if (currentChatId && uuidRegex.test(currentChatId)) {
      dispatch(switchConversationContext(currentChatId));

      // Clear streaming flags when switching conversations
      if (prevConversationIdRef.current !== currentChatId) {
        console.log(
          '[ExistingChatIdPage] Clearing streaming flags for conversation switch',
        );
        dispatch(setStreamJustCompleted(false));
        
        // Abort any active streaming request from the previous conversation
        if (abortControllerRef.current && 
            activeStreamingConversationId && 
            activeStreamingConversationId !== currentChatId) {
          console.log(
            `[ExistingChatIdPage] Aborting stream for conversation ${activeStreamingConversationId} when switching to ${currentChatId}`
          );
          abortControllerRef.current.abort();
          abortControllerRef.current = null;
          dispatch(setActiveStreamingConversation(undefined));
        }
      }
      
      // Update the previous conversation ID reference
      prevConversationIdRef.current = currentChatId;
    } else {
      // Handle switching to new conversation context (chatId is 'new' or invalid)
      dispatch(switchConversationContext(undefined));
    }
  }, [chatId, dispatch, activeStreamingConversationId]);

  // Effect to handle redirecting from a "new" state to the actual conversation URL once ID is available
  useEffect(() => {
    const currentUrlChatId = Array.isArray(chatId) ? chatId[0] : chatId;
    const uuidRegex =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    const isUrlLikelyNew =
      !currentUrlChatId || !uuidRegex.test(currentUrlChatId);

    // console.log(`[RedirectEffect] Running: isUrlLikelyNew=${isUrlLikelyNew}, reduxConversationId=${reduxConversationId}, isValidUUID=${reduxConversationId ? uuidRegex.test(reduxConversationId) : 'N/A'}, isStreamingActive=${isStreamingActiveForThisConversation}`);

    if (
      isUrlLikelyNew &&
      reduxConversationId &&
      uuidRegex.test(reduxConversationId) &&
      !isStreamingActiveForThisConversation // Don't redirect while streaming is active
    ) {
      // console.log(`[RedirectEffect] Conditions met. Redirecting to conversation URL: /chat/${reduxConversationId}`);
      router.replace(`/chat/${reduxConversationId}`);
    }
  }, [reduxConversationId, chatId, router, isStreamingActiveForThisConversation]);

  // Helper function for flexible model matching
  const findModelByName = useCallback(
    (searchName: string, modelList: GptModel[]) => {
      if (!searchName) return null;

      // Strategy 1: Exact match on display_name
      let match = modelList.find((m) => m.display_name === searchName);
      if (match) {
        return match;
      }

      // Strategy 2: Exact match on model_name
      match = modelList.find((m) => m.model_name === searchName);
      if (match) {
        return match;
      }

      // Strategy 3: Case-insensitive match on display_name
      match = modelList.find(
        (m) => m.display_name?.toLowerCase() === searchName.toLowerCase(),
      );
      if (match) {
        return match;
      }

      // Strategy 4: Case-insensitive match on model_name
      match = modelList.find(
        (m) => m.model_name?.toLowerCase() === searchName.toLowerCase(),
      );
      if (match) {
        return match;
      }

      // Strategy 5: Advanced fuzzy matching with multiple approaches
      const normalizeString = (str: string) =>
        str
          .toLowerCase()
          .replace(/[-_\s]/g, '')
          .replace(/[^a-z0-9]/g, '');

      const normalizedSearch = normalizeString(searchName);

      // First, try exact model mappings (most precise)
      const exactModelMappings: Record<string, string[]> = {
        // Map specific model identifiers to their historical names
        gpt4omini: ['chatgpt4omini', 'chatgpt4o-mini'],
        gpt4o: ['chatgpt4o', 'chatgpt4ostandard', 'chatgpt4o-standard'],
        gpt41: ['chatgpt41', 'chatgpt-41', 'chatgpt-4.1'],
        gpt41mini: ['chatgpt41mini', 'chatgpt-41-mini', 'chatgpt-4.1-mini'],
      };

      for (const [canonical, variants] of Object.entries(exactModelMappings)) {
        // Check if the search term exactly matches one of the variants
        const searchMatchesVariant = variants.some((variant) => {
          const normalizedVariant = normalizeString(variant);
          return normalizedSearch === normalizedVariant;
        });

        if (searchMatchesVariant) {
          // Find the model that matches this canonical pattern
          const exactMatch = modelList.find((m) => {
            const normalizedDisplayName = normalizeString(m.display_name || '');
            const normalizedModelName = normalizeString(m.model_name || '');
            return (
              normalizedModelName === canonical ||
              normalizedDisplayName === canonical ||
              normalizedModelName.includes(canonical) ||
              normalizedDisplayName.includes(canonical)
            );
          });

          if (exactMatch) {
            return exactMatch;
          }
        }
      }

      // Fallback to other fuzzy matching approaches
      match = modelList.find((m) => {
        const normalizedDisplayName = normalizeString(m.display_name || '');
        const normalizedModelName = normalizeString(m.model_name || '');

        // Approach 5a: Exact normalized match
        if (
          normalizedDisplayName === normalizedSearch ||
          normalizedModelName === normalizedSearch
        ) {
          return true;
        }

        // Approach 5b: Check if normalized search contains the model name or vice versa
        // But ensure it's a meaningful match (not just partial overlap) and be more strict
        const searchInModel =
          normalizedSearch.includes(normalizedModelName) &&
          normalizedModelName.length >= 5;
        const modelInSearch =
          normalizedModelName.includes(normalizedSearch) &&
          normalizedSearch.length >= 5;
        const searchInDisplay =
          normalizedSearch.includes(normalizedDisplayName) &&
          normalizedDisplayName.length >= 5;
        const displayInSearch =
          normalizedDisplayName.includes(normalizedSearch) &&
          normalizedSearch.length >= 5;

        if (
          searchInModel ||
          modelInSearch ||
          searchInDisplay ||
          displayInSearch
        ) {
          return true;
        }

        return false;
      });

      if (match) {
        return match;
      }

      return null;
    },
    [],
  );

  // --- Effect to update messages in Redux store after history fetch ---
  useEffect(() => {
    if (isHistorySuccess && historyMessages) {
      dispatch(setChatMessages(historyMessages));
    }
  }, [isHistorySuccess, historyMessages, dispatch]);

  // --- Effect to trigger refetch after streaming ends ---
  useEffect(() => {
    // Only trigger refetch when streaming goes from true to false AND the query is not skipped
    const isQuerySkipped = !reduxConversationId ||
      !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(reduxConversationId) ||
      !modelList || modelList.length === 0 ||
      isStreamingActiveForThisConversation;

    if (prevStreamingStatusRef.current && !isStreamingActiveForThisConversation && !isQuerySkipped) {
      console.log('[ExistingChatIdPage] Streaming ended, triggering refetch to load updated history');
      refetch();
    }

    // Update the ref for the next render
    prevStreamingStatusRef.current = isStreamingActiveForThisConversation;
  }, [isStreamingActiveForThisConversation, refetch, reduxConversationId, modelList]);

  // Effect to keep the ref updated with the latest state
  useEffect(() => {
    tempFileListRef.current = tempFileList;
  }, [tempFileList]);

  // Cleanup effect to abort streaming when component unmounts or conversation changes
  useEffect(() => {
    return () => {
      // Cleanup any active streaming when component unmounts
      if (activeStreamingConversationId) {
        console.log('[ExistingChatIdPage] Cleaning up active stream on unmount');
        dispatch(setActiveStreamingConversation(undefined));
      }
    };
  }, [activeStreamingConversationId, dispatch]);

  // Effect to scroll the main chat area down when messages change
  useEffect(() => {
    if (scrollContainerRef.current) {
      // Use setTimeout to allow the DOM to update before scrolling
      setTimeout(() => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop =
            scrollContainerRef.current.scrollHeight;
        }
      }, 0);
    }
  }, [messages]); // Dependency: messages array from Redux

  // --- Side Panel Handler ---
  const handleOpenSourcesPanel = useCallback((sources: Source[]) => {
    setPanelSources(sources);
    setIsSourcesPanelOpen(true);
  }, []);

  const handleCloseSourcesPanel = useCallback(() => {
    setIsSourcesPanelOpen(false);
    // Optionally delay clearing sources if needed for animations
    // setTimeout(() => setPanelSources(undefined), 300);
  }, []);

  // --- Model Parameters Modal Handlers ---
  const handleOpenParamsModal = useCallback(() => {
    setIsParamsModalOpen(true);
  }, []);

  const handleCloseParamsModal = useCallback(() => {
    setIsParamsModalOpen(false);
  }, []);

  // --- Share Modal Handlers ---
  const handleOpenShareModal = useCallback(() => {
    setIsShareModalOpen(true);
  }, []);

  const handleCloseShareModal = useCallback(() => {
    setIsShareModalOpen(false);
  }, []);

  const handleOpenPromptGallery = useCallback(() => {
    setIsPromptGalleryOpen(true);
  }, []);

  const handleClosePromptGallery = useCallback(() => {
    setIsPromptGalleryOpen(false);
  }, []);

  const handlePromptSelectFromGallery = useCallback(
    (prompt: { prompt_content: string; system_instruction: string }) => {
      setPromptContent(prompt.prompt_content);
      setSystemInstruction(prompt.system_instruction);
      setIsPromptGalleryOpen(false); // Close modal after selection
    },
    [setPromptContent, setSystemInstruction, setIsPromptGalleryOpen],
  );

  const handlePromptSelect = useCallback(
    (prompt: any) => {
      if (reduxConversationId) {
        setPromptContent(prompt.prompt_content);
        setSystemInstruction(prompt.system_instruction); // Set system instruction directly
        // No need to call updateConversationParams here, it will be handled on submit
      } else {
        // This part should ideally not be reached if the modal is opened from an existing chat page
        // But as a fallback, if it were to navigate to new chat, it would use sessionStorage
        sessionStorage.setItem('promptContent', prompt.prompt_content);
        sessionStorage.setItem('systemInstruction', prompt.system_instruction);
        router.push(`/chat/new`);
      }
      handleClosePromptGallery();
    },
    [
      reduxConversationId,
      router,
      handleClosePromptGallery,
      setPromptContent,
      setSystemInstruction,
    ],
  );

  // --- Prompt Rewrite Assistant Handlers ---
  const handleOpenPromptAssistant = useCallback((currentPrompt: string) => {
    setPromptAssistantInitialContent(currentPrompt);
    setIsPromptAssistantOpen(true);
  }, []);

  const handleClosePromptAssistant = useCallback(() => {
    setIsPromptAssistantOpen(false);
    setPromptAssistantInitialContent('');
  }, []);

  const handleAcceptRewrittenPrompt = useCallback(
    (newPrompt: string) => {
      setPromptContent(newPrompt);
      handleClosePromptAssistant();
    },
    [setPromptContent, handleClosePromptAssistant],
  );

  const handleShare = useCallback(async () => {
    const conversationId = Array.isArray(chatId) ? chatId[0] : chatId;
    if (!conversationId) {
      throw new Error('No conversation ID available');
    }
    return shareConversation(conversationId).unwrap();
  }, [chatId, shareConversation]);

  // --- Regenerate Handler ---
  const handleRegenerate = useCallback(async () => {
    console.log('[REGEN] Starting regeneration process...');
    console.log('[REGEN] Current messages before regeneration:', messages.length, messages);
    
    // Find the last assistant message to get the model used
    const lastAssistantMessageIndex = messages.findLastIndex(
      (msg) => msg.role === 'assistant',
    );
    const lastUserMessageIndex = messages.findLastIndex(
      (msg) => msg.role === 'user',
    );

    console.log('[REGEN] Last assistant message index:', lastAssistantMessageIndex);
    console.log('[REGEN] Last user message index:', lastUserMessageIndex);

    if (lastAssistantMessageIndex === -1 || lastUserMessageIndex === -1) {
      console.log('[REGEN] No valid assistant or user message found, aborting');
      return;
    }

    const lastAssistantMessage = messages[lastAssistantMessageIndex];
    const lastUserMessage = messages[lastUserMessageIndex];

    console.log('[REGEN] Last assistant message:', lastAssistantMessage);
    console.log('[REGEN] Last user message:', lastUserMessage);

    // Get the model used in the last assistant response
    const modelToUse =
      lastAssistantMessage.model_api_name || selectedNextModelName || '';

    console.log('[REGEN] Model to use:', modelToUse);
    console.log('[REGEN] Chat session ID:', Array.isArray(chatId) ? chatId[0] : chatId);

    // Remove the last assistant message
    console.log('[REGEN] Dispatching prepareForRegeneration...');
    dispatch(prepareForRegeneration());

    // Re-submit the last user message with the same model
    const requestBody: Omit<ChatCompletionRequest, 'stream'> = {
      prompt: lastUserMessage.content,
      model: modelToUse,
      chat_session_id:
        (Array.isArray(chatId) ? chatId[0] : chatId) || undefined,
      useGoogle: useGoogleEnabled,
      instructions: systemInstruction || undefined,
      isRegeneration: true, // Mark this as a regeneration request
      usedMention: lastUserMessage.usedMention || false, // Preserve the original usedMention value
      // Note: File attachments from the original message are not re-sent in regeneration
      // The pastMessagesCount is handled automatically via the conversation session
    };

    console.log('[REGEN] Request body:', requestBody);

    try {
      console.log('[REGEN] Calling triggerChatCompletion...');
      await triggerChatCompletion(requestBody).unwrap();
      console.log('[REGEN] triggerChatCompletion completed successfully');
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error('[REGEN] Failed to regenerate response:', errorMessage, error);
      dispatch(setChatErrorMessage('Failed to regenerate response'));
    }
  }, [
    messages,
    selectedNextModelName,
    chatId,
    useGoogleEnabled,
    systemInstruction,
    triggerChatCompletion,
    dispatch,
  ]);

  // --- Input Area Handlers ---
  const handleReset = useCallback(() => {
    if (messageInputRef.current) messageInputRef.current.value = '';
    setTempFileList([]);
    dispatch(setIsThinking(false));
  }, [dispatch]);

  const handleStopGenerating = useCallback(() => {
    dispatch(setIsThinking(false));
    console.warn('Stop generating functionality needs implementation.');
  }, [dispatch]);

  const handleTextChange = useCallback(() => {
    /* Input change logic if needed */
  }, []);

  // --- Main Submit Logic ---
  const handleSubmit = useCallback(
    async (e?: FormEvent<HTMLFormElement> | MouseEvent<HTMLButtonElement>) => {
      e?.preventDefault();

      const rawMessageContent = messageInputRef.current?.value?.trim() || '';
      // Strip all model mentions from the message content before sending to API
      const messageContent = stripAllModelMentions(
        rawMessageContent,
        selectedModelMentions,
        modelList,
      );

      // Read from ref inside the callback
      const currentFiles = tempFileListRef.current;
      // Check if there's an active stream for a different conversation
      const currentConversationId = Array.isArray(chatId) ? chatId[0] : chatId;
      if (activeStreamingConversationId && 
          activeStreamingConversationId !== currentConversationId) {
        dispatch(
          setChatErrorMessage(
            'Please wait for the current response to complete before starting a new conversation.',
          ),
        );
        return;
      }

      // Use either selectedNextModelName or conversationModel for validation
      const effectiveModelName = selectedNextModelName || conversationModel;
      if (
        !effectiveModelName ||
        (!messageContent && currentFiles.length === 0)
      ) {
        dispatch(
          setChatErrorMessage(
            'Please enter a message and ensure a model is selected.',
          ),
        );
        return;
      }

      dispatch(setChatErrorMessage(undefined));

      // --- Prepare file data ---
      let uploadedFilesData: {
        filename: string;
        mimeType: string;
        content: string;
      }[] = [];
      // Use the ref's current value here
      if (currentFiles.length > 0) {
        try {
          uploadedFilesData = await Promise.all(
            currentFiles.map(async (file) => {
              // Iterate over currentFiles from ref
              const base64Content = await readFileAsBase64(file);
              return {
                filename: file.name,
                mimeType: file.type,
                content: base64Content,
              };
            }),
          );
        } catch (error) {
          console.error('Error reading files:', error);
          dispatch(setChatErrorMessage('Failed to read files for upload.'));
          return; // Stop submission if file reading fails
        }
      }

      // --- Add user message to UI (include file indication) ---
      // Use the ref's current value here too
      const attachmentsData = currentFiles.map((f) => ({
        name: f.name,
        type: f.type,
        size: f.size,
      }));

      // Find the selected model object using the effective model name
      const selectedModel = findModelByName(effectiveModelName, modelList);
      if (!selectedModel) {
        dispatch(
          setChatErrorMessage('Selected model not found. Please select a valid model.'),
        );
        return;
      }

      // Check if any model was mentioned via @mention
      const wasAnyModelMentioned = selectedModelMentions.size > 0;

      const userMessage: Conversation = {
        role: 'user',
        content: messageContent, // Only include the typed text content
        attachments: attachmentsData.length > 0 ? attachmentsData : undefined, // Add structured attachment data
        messageId: uuidv4(),
        timestamp: Date.now(),
        model_display_name: selectedModel.display_name,
        model_api_name: selectedModel.model_name,
        usedMention: wasAnyModelMentioned,
      };
      dispatch(addUserMessage(userMessage));

      // --- Prepare request body ---
      const currentChatSessionId = (Array.isArray(chatId) ? chatId[0] : chatId) || undefined;
      const isNewConversation = !currentChatSessionId || currentChatSessionId === 'new';
      
      const requestBody: Omit<ChatCompletionRequest, 'stream'> = {
        prompt: messageContent, // Send only the text prompt here
        model: effectiveModelName, // Use the effective model name (either selected or conversation default)
        chat_session_id: currentChatSessionId,
        useGoogle: useGoogleEnabled,
        instructions: systemInstruction || undefined, // Pass systemInstruction
        files: uploadedFilesData.length > 0 ? uploadedFilesData : undefined, // Add files data
        usedMention: wasAnyModelMentioned, // Track if user used @mention
        // Add pending parameters for new conversations
        ...(isNewConversation && pendingParameters ? {
          pendingInstructions: pendingParameters.instructions || undefined,
          pendingPastMessagesCount: pendingParameters.pastMessagesCount || undefined,
          pendingMaxResponseTokens: pendingParameters.maxResponseTokens || undefined,
          pendingTemperature: pendingParameters.temperature || undefined,
          pendingTopP: pendingParameters.topP || undefined,
        } : {}),
      };

      // Debug logging for pending parameters
      if (isNewConversation) {
        
        if (pendingParameters) {
        } else {
        }
      }

      if (messageInputRef.current) messageInputRef.current.value = '';
      setPromptContent(''); // Clear the React state as well
      setTempFileList([]); // Clear the state after processing files from ref

      // Clear selected model mentions after successful submission
      if (clearSelectedMentionsRef.current) {
        clearSelectedMentionsRef.current();
      }

      // Mark this conversation as recently active
      if (reduxConversationId) {
        recentlyActiveChatsRef.current.add(reduxConversationId);
        // Clear the flag after some time
        setTimeout(() => {
          recentlyActiveChatsRef.current.delete(reduxConversationId);
        }, 30000); // Clear after 30 seconds
      }

      try {
        await triggerChatCompletion(requestBody).unwrap();
        
        // Clear pending parameters after successful new conversation creation
        if (isNewConversation && pendingParameters) {
          dispatch(clearPendingParameters());
        }
      } catch (error: any) {
        if (error.data?.errorCode === 'FILE_PROCESSING_FAILED') {
          dispatch(
            setChatErrorMessage(
              `The file may be corrupt, password-protected, or in an unsupported format. If the document has a security label, please set it to 'Public' and try again.`,
            ),
          );
          // Remove the optimistic user message on failure
          dispatch(triggerClearMessage());
        } else {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          console.error(
            'Failed to initiate chat completion:',
            errorMessage,
            error,
          );
          // Also remove the message for other errors
          dispatch(triggerClearMessage());
        }
      }
    },
    [
      selectedNextModelName,
      conversationModel,
      chatId,
      selectedModelMentions, // Add selectedModelMentions to dependencies
      modelList,
      useGoogleEnabled,
      triggerChatCompletion,
      reduxConversationId,
      setPromptContent,
      systemInstruction,
      dispatch,
      findModelByName,
      activeStreamingConversationId, // Add activeStreamingConversationId to dependencies
    ],
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Enter' && !e.shiftKey && !isSubmitting) {
        e.preventDefault();
        handleSubmit();
      }
    },
    [isSubmitting, handleSubmit],
  );

  const LoadingIndicator = () =>
    isAiTyping ? (
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', px: 1, mb: 2 }}>
      </Box>
    ) : null;

  const SourceContent = (
    <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
      {panelSources && panelSources.length > 0 ? (
        <List disablePadding>
          {panelSources.map((source, index) => (
            <ListItem
              key={index}
              divider
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                py: 1.5,
                px: 2,
              }}
            >
              {/* Number Circle */}
              <Box
                sx={{
                  minWidth: 24,
                  width: 24,
                  height: 24,
                  borderRadius: '50%',
                  bgcolor: 'action.selected',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1.5,
                  mt: 0.5,
                }}
              >
                <Typography
                  variant="caption"
                  sx={{ fontWeight: 'medium', color: 'text.primary' }}
                >
                  {index + 1}
                </Typography>
              </Box>
              {/* Text Content */}
              <ListItemText
                primary={
                  <Link
                    href={source.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    underline="hover"
                    color='text.main'
                    variant="body1"
                    // sx={{
                    //   fontWeight: 'medium',
                    //   display: 'block',
                    //   overflow: 'hidden',
                    //   textOverflow: 'ellipsis',
                    // }}
                  >
                    {source.title}
                  </Link>
                }
                secondary={
                  <>
                    <Link
                      href={source.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      variant="caption"
                      sx={{
                        display: 'block',
                        color: 'text.secondary',
                        textDecoration: 'none',
                        '&:hover': { textDecoration: 'underline' },
                        wordBreak: 'break-all',
                      }}
                    >
                      {new URL(source.link).hostname}
                    </Link>
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.secondary"
                      sx={{ display: 'block', mt: 0.5 }}
                    >
                      {source.snippet}
                    </Typography>
                  </>
                }
                sx={{ m: 0 }}
              />
            </ListItem>
          ))}
        </List>
      ) : (
        <Typography sx={{ p: 2 }}>No sources to display.</Typography>
      )}
    </Box>
  );

  return (
    // Use flex row for main content + side panel
    <Box
      sx={{ display: 'flex', height: '100svh', bgcolor: 'background.default' }}
    >
      {/* Main Chat Area */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flexGrow: 1,
          height: '100svh',
          width: isSourcesPanelOpen ? '65%' : '100%',
          transition: 'width 0.3s ease-in-out',
          position: 'relative',
        }}
      >
        {/* Pass the handler to ChatHeader */}
        <ChatHeader
          chatId={(Array.isArray(chatId) ? chatId[0] : chatId) || ''}
          chatTopic={conversationTitle || `Chat Session ${chatId || 'New'}`}
          onOpenParameters={handleOpenParamsModal} // Pass handler
          onShare={handleOpenShareModal} // Pass share handler
        />

        {/* Assign the ref to the scrollable Box */}
        <Box
          ref={scrollContainerRef}
          sx={{
            flexGrow: 1,
            overflowY: 'auto',
            position: 'relative',
          }}
        >
          {isHistoryLoading && (
            <Box
              sx={{
                position: 'absolute',
                inset: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? 'rgba(0,0,0,0.5)'
                    : 'rgba(255,255,255,0.75)',
                zIndex: 10,
              }}
            >
              <CircularProgress />
            </Box>
          )}
          {isHistoryError && !isHistoryLoading && (
            <Box sx={{ p: 2, color: 'red' }}>
              Error loading history:{' '}
              {(historyErrorData as any)?.data?.message ||
                (historyErrorData as any)?.error ||
                'Unknown error'}
            </Box>
          )}
          {!isHistoryLoading && !isHistoryError && (
            <>
              {/* {console.log('[ExistingChatIdPage] Rendering ConversationDisplay with messages:', messages)} */}
              {/* Pass the handler to ConversationDisplay */}
              <ConversationDisplay
                messages={messages}
                onOpenSourcesPanel={handleOpenSourcesPanel}
                onRegenerate={handleRegenerate}
                isHistoryLoading={isHistoryLoading}
              />
            </>
          )}
          <LoadingIndicator />
          <ChatErrorDisplay />
        </Box>

        <Box
          sx={{
            p: 2,
            position: 'relative',
            zIndex: 1,
          }}
        >
          <ChatInputArea
            isLoading={isSubmitting}
            tempFileList={tempFileList}
            setTempFileList={setTempFileList}
            handleReset={handleReset}
            handleSubmit={handleSubmit}
            onTextChange={handleTextChange}
            onKeyDown={handleKeyDown}
            supportedFileList={supportedFileList || ''}
            visionModelList={visionModelList}
            disableFileUpload={false}
            onClickStopButton={handleStopGenerating}
            uploadFileSizeLimit={parseInt(
              process.env.NEXT_PUBLIC_UPLOAD_FILE_SIZE_LIMIT || '10000000',
              10,
            )}
            sizeExceedsMessage={'File size exceeds limit.'}
            setChatErrorMessage={(msg: string | undefined) =>
              dispatch(setChatErrorMessage(msg))
            }
            messageInputRef={messageInputRef}
            promptContent={promptContent}
            setPromptContent={setPromptContent}
            systemInstruction={systemInstruction} // Pass systemInstruction
            setSystemInstruction={setSystemInstruction} // Pass setSystemInstruction
            useGoogleEnabled={useGoogleEnabled}
            setUseGoogleEnabled={setUseGoogleEnabled}
            onSelectedMentionsChange={setSelectedModelMentions}
            clearSelectedMentionsRef={clearSelectedMentionsRef}
            onPromptSelect={handleOpenPromptGallery}
            onPromptAssistantClick={handleOpenPromptAssistant} // Pass the new handler
          />
        </Box>
      </Box>

      {/* Render the Model Parameters Modal */}
      <ModelParametersModal
        open={isParamsModalOpen}
        onClose={handleCloseParamsModal}
        conversationId={currentChatId} // Pass the current chat ID directly
      />

      {/* Render the Share Conversation Modal */}
      <ShareConversationModal
        open={isShareModalOpen}
        onClose={handleCloseShareModal}
        conversationId={(Array.isArray(chatId) ? chatId[0] : chatId) || ''}
        onShare={handleShare}
      />

      <PromptGalleryModal
        open={isPromptGalleryOpen}
        handleClose={handleClosePromptGallery}
        handlePromptSelect={handlePromptSelect} // Use the correct handler
        isExistingChat={!!chatId}
      />

      {/* Render the Prompt Rewrite Assistant Modal */}
      <PromptRewriteAssistantModal
        open={isPromptAssistantOpen}
        onClose={handleClosePromptAssistant}
        currentPrompt={promptAssistantInitialContent}
        onApply={handleAcceptRewrittenPrompt}
        onRewrite={async (prompt) => {
          const response = await rewritePrompt({ prompt }).unwrap();
          return response.prompt;
        }}
        onGenerate={async (idea) => {
          const response = await generatePrompt({ idea }).unwrap();
          return response.prompt;
        }}
      />

      {/* Sources Side Panel */}
      {isMobile ? (
        <ModalContainer
          open={isSourcesPanelOpen}
          handleClose={handleCloseSourcesPanel}
        >
          <DialogTitle textAlign={'center'}> Sources</DialogTitle>
          {SourceContent}
        </ModalContainer>
      ) : (
        <Paper
          elevation={4}
          sx={{
            position: { xs: 'absolute', md: 'unset' },
            bottom: 0,
            width: { xs: '100%', md: isSourcesPanelOpen ? '35%' : 0 }, // Adjust width as needed
            height: isSourcesPanelOpen ? '100svh' : 0,
            display: { xs: 'none', md: 'flex' },
            flexDirection: 'column',
            borderLeft: '1px solid',
            borderColor: 'divider',
            transition: {
              xs: 'height 0.3s ease-in-out',
              md: 'width 0.3s ease-in-out',
            }, // Smooth transition
            overflow: 'hidden', // Prevent content overflow during transition
          }}
        >
          <Box
            sx={{
              position: 'relative',
              borderBottom: '1px solid',
              borderColor: 'divider',
              bgcolor: 'background.paper',
              py: 0.5,
            }}
          >
            <Toolbar
              variant="dense"
              disableGutters
              sx={{
                justifyContent: 'space-between',
                position: 'relative',
                px: 2,
              }}
            >
              <Box
                width={34}
                height={34}
                display={{ xs: undefined, md: 'none' }}
              />
              <Typography
                variant="h6"
                sx={{
                  flex: 1,
                  textAlign: { xs: 'center', md: 'unset' },
                  fontWeight: 'medium',
                }}
              >
                Sources
              </Typography>
              <IconButton onClick={handleCloseSourcesPanel} size="small">
                <CloseIcon />
              </IconButton>
            </Toolbar>
          </Box>
          {SourceContent}
        </Paper>
      )}
    </Box>
  );
}
