import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>q,
  <PERSON><PERSON>,
  HttpException,
  HttpStatus,
  Logger,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ImageStorageService } from '../utils/image-storage.service';
import { AuthenticatedUser } from '../auth/user.interface';

@ApiTags('Images')
@Controller('general/images')
export class ImageController {
  private readonly logger = new Logger(ImageController.name);

  constructor(private readonly imageStorageService: ImageStorageService) {}

  @Get('*')
  @ApiOperation({
    summary: 'Retrieve stored image',
    description: 'Retrieves an image from the shared drive storage. Path format: /api/general/images/{date}/{userId}/{filename}',
  })

  @ApiResponse({
    status: 200,
    description: 'Image retrieved successfully',
    content: {
      'image/jpeg': { schema: { type: 'string', format: 'binary' } },
      'image/png': { schema: { type: 'string', format: 'binary' } },
      'image/gif': { schema: { type: 'string', format: 'binary' } },
    },
  })
  @ApiResponse({ status: 403, description: 'Access denied' })
  @ApiResponse({ status: 404, description: 'Image not found' })
  async getImage(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    // Extract image path from URL, removing the controller prefix
    const fullPath = req.url || '';
    const basePrefix = '/api/general/images/';
    const imagePath = fullPath.startsWith(basePrefix)
      ? fullPath.substring(basePrefix.length)
      : fullPath;

    try {
      const user = req.user as AuthenticatedUser;
      if (!user?.userId) {
        throw new ForbiddenException('User authentication required');
      }

      // Validate image path format
      if (!imagePath || imagePath.length === 0) {
        throw new NotFoundException('Image path is required');
      }

      this.logger.log(`Image request: ${imagePath} by user ${user.userId}`);

      // Get image from shared drive
      const imageBuffer = await this.imageStorageService.getImageFromSharedDrive(
        imagePath,
        user.userId,
      );

      // Determine content type from file extension
      const contentType = this.getContentTypeFromPath(imagePath);

      // Set appropriate headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
      res.setHeader('Content-Length', imageBuffer.length);

      // Send image
      res.send(imageBuffer);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : String(error);

      this.logger.error(`Failed to retrieve image ${imagePath}:`, errorStack);

      if (errorMessage.includes('Access denied')) {
        throw new ForbiddenException('Access denied to this image');
      }
      if (errorMessage.includes('not found')) {
        throw new NotFoundException('Image not found');
      }
      throw new HttpException(
        'Failed to retrieve image',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Determines content type from file path
   */
  private getContentTypeFromPath(imagePath: string): string {
    const extension = imagePath.toLowerCase().split('.').pop();
    
    const extensionToContentType: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      bmp: 'image/bmp',
      webp: 'image/webp',
      tiff: 'image/tiff',
      tif: 'image/tiff',
    };

    return extension ? (extensionToContentType[extension] || 'image/jpeg') : 'image/jpeg';
  }
}
