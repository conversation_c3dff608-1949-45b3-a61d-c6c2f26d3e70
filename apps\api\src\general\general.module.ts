import { Module, forwardRef } from '@nestjs/common';
import { GeneralController } from './general.controller'; // Import correct controller
import { GeneralService } from './general.service'; // Import correct service
import { ImageController } from './image.controller'; // Import Image controller
import { RecentModelsRestController } from './recent-models/recent-models-rest.controller'; // Import REST controller
import { RestModelsController } from './rest-models/rest-models.controller'; // Import REST Models controller
// OcrService is now provided by UtilsModule
// PrismaService is provided via PrismaModule imported in AppModule
// MailerService is provided via MailerModule imported in AppModule
// import { ChatCompletionModule } from './chat-completion/chat-completion.module'; // Moved to ChatModule
import { ChatModule } from './chat/chat.module'; // Import moved ChatModule
import { SpeechModule } from './speech/speech.module'; // Import moved SpeechModule
import { EmbeddingsModule } from './embeddings/embeddings.module'; // Import EmbeddingsModule
import { UtilsModule } from '../utils/utils.module'; // Import UtilsModule
import { PromptGalleryModule } from './prompt-gallery/prompt-gallery.module';
import { TasksModule } from './tasks/tasks.module';
import { PromptRewriteController } from './prompt-rewrite.controller';
import { PromptRewriteService } from './prompt-rewrite.service';
import { LlmModule } from '../llm/llm.module';

@Module({
  imports: [
    // ChatCompletionModule, // Removed: Now imported by ChatModule
    forwardRef(() => ChatModule), // <-- Use forwardRef for ChatModule
    SpeechModule, // Add moved SpeechModule
    EmbeddingsModule, // Add EmbeddingsModule
    UtilsModule, // Import UtilsModule to provide OcrService to GeneralController
    PromptGalleryModule,
    TasksModule,
    LlmModule,
  ],
  controllers: [
    GeneralController,
    ImageController,
    RecentModelsRestController,
    RestModelsController,
    PromptRewriteController,
  ], // Declare controllers
  providers: [
    GeneralService, // Provide GeneralService
    PromptRewriteService,
    // OcrService removed from here
    // PrismaService and MailerService are available via imports in AppModule
  ],
  exports: [GeneralService], // Export GeneralService so other modules can use it
})
export class GeneralModule {} // Renamed module
