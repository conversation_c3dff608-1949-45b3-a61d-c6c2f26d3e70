# Tutorial Plan

This document outlines the steps for the new user tutorial.

## Library

*   **Library:** `react-joyride`

## Database Schema

A new field will be added to the `acl_user` table to track whether a user has completed the tutorial.

*   **Table:** `acl_user`
*   **Field:** `has_completed_tutorial`
*   **Type:** `Boolean`
*   **Default:** `false`

## API Endpoint

An API endpoint will be created to update the user's tutorial completion status.

*   **Endpoint:** `/api/user/complete-tutorial`
*   **Method:** `POST`
*   **Description:** Sets `has_completed_tutorial` to `true` for the currently authenticated user.

## Tutorial Steps

The tutorial will consist of the following steps, highlighting key features of the application:

| Step | Target Element | Title | Content |
| :--- | :--- | :--- | :--- |
| 1 | `body` | Welcome to the Platform! | This is a quick tour to get you started. |
| 2 | `.model-selection-area` | Select a Model | You can choose from a variety of models here. |
| 3 | `.chat-input-area` | Type Your Message | This is where you can type your message to the AI. |
| 4 | `.send-button` | Send Your Message | Click here to send your message. |
| 5 | `.chat-history-sidebar` | Your Conversations | Your past conversations will be saved here. |
| 6 | `.share-chat-button` | Share Your Chat | You can share your conversation with others. |
| 7 | `.settings-button` | Adjust Settings | Customize your experience in the settings menu. |
| 8 | `body` | Enjoy the Platform! | You're all set! Feel free to explore. |

## Settings UI

A button will be added to the settings page to allow users to restart the tutorial.

*   **Location:** Settings > Appearance
*   **Button Text:** "Restart Tutorial"

## Diagram

Here is a diagram illustrating the updated flow:

```mermaid
graph TD
    subgraph "Frontend"
        A[Start Tutorial] --> B{Run react-joyride};
        B --> C{Tutorial Complete};
        C --> D[Call API];
        E[Settings Page] --> F{Restart Tutorial};
        F --> A;
    end

    subgraph "Backend"
        D --> G[/api/user/complete-tutorial];
        G --> H[Update User in DB];
    end

    subgraph "Database"
        H --> I(acl_user.has_completed_tutorial);
    end