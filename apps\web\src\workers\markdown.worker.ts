import { marked } from 'marked';
import type { Tokens } from 'marked';
import hljs from 'highlight.js';
import { linkifyCitations, parseThinkBlocks } from '@/lib/utils/markdownUtils';
import { Source } from '@/lib/store/chatSlice';

// Configure marked renderer for syntax highlighting
const renderer = new marked.Renderer();
const originalCode = renderer.code;

renderer.code = function(token: Tokens.Code) {
  const { text, lang, escaped } = token;
  if (lang && hljs.getLanguage(lang)) {
    try {
      const highlighted = hljs.highlight(text, { language: lang }).value;
      return `<pre><code class="hljs language-${lang}">${highlighted}</code></pre>`;
    } catch (err) {
      // Fall back to original if highlighting fails
    }
  }
  
  // Use original renderer as fallback
  return originalCode.call(this, token);
};

marked.setOptions({
  renderer: renderer,
  breaks: true,
  gfm: true,
});

self.onmessage = (event: MessageEvent<{ content: string; sources: Source[]; messageId: string }>) => {
  const { content, sources, messageId } = event.data;

  // Perform the heavy lifting in the worker
  const { regularContent, thinkingContent } = parseThinkBlocks(content);
  const linkifiedContent = linkifyCitations(regularContent, sources);
  const processedHtml = marked.parse(linkifiedContent) as string;

  // Post the processed HTML back to the main thread
  self.postMessage({
    html: processedHtml,
    thinkingContent: thinkingContent,
    messageId,
  });
};