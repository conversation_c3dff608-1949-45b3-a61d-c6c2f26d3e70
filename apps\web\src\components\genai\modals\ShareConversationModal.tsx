import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CloseIcon from '@mui/icons-material/Close';
import ShareIcon from '@mui/icons-material/Share';

interface ShareConversationModalProps {
  open: boolean;
  onClose: () => void;
  conversationId: string;
  onShare: () => Promise<{ shareId: string; shareUrl: string }>;
}

export const ShareConversationModal: React.FC<ShareConversationModalProps> = ({
  open,
  onClose,
  conversationId,
  onShare,
}) => {
  const [loading, setLoading] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const handleShare = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await onShare();
      const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
      const fullUrl = `${window.location.origin}${basePath}${result.shareUrl}`;
      setShareUrl(fullUrl);
    } catch (err) {
      setError('Failed to generate share link. Please try again.');
      console.error('Share error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    if (!shareUrl) return;

    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleClose = () => {
    setShareUrl(null);
    setError(null);
    setCopied(false);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        },
      }}
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(3px)',
          backgroundColor: 'rgba(0,0,0,0.1)',
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            <ShareIcon />
            <Typography variant="h6">Share Conversation</Typography>
          </Box>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {!shareUrl && !error && (
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Generate a shareable link for this conversation. Anyone with the
              link who is logged in can create their own copy of this
              conversation.
            </Typography>

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                • Only authenticated users can access shared conversations
                <br />
                • Recipients will get a copy of the conversation up to this
                point
                <br />• The copy will be independent - changes won't sync
                between copies
              </Typography>
            </Alert>
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {shareUrl && (
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Share this link to allow others to access a copy of this
              conversation:
            </Typography>

            <TextField
              fullWidth
              value={shareUrl}
              variant="outlined"
              sx={{ mt: 2 }}
              InputProps={{
                readOnly: true,
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={handleCopy} edge="end">
                      <ContentCopyIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {copied && (
              <Typography
                variant="caption"
                color="success.main"
                sx={{ mt: 1, display: 'block' }}
              >
                Copied to clipboard!
              </Typography>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        {!shareUrl && (
          <Button
            onClick={handleShare}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <ShareIcon />}
            sx={{ borderRadius: 2 }}
          >
            {loading ? 'Generating...' : 'Generate Link'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};
