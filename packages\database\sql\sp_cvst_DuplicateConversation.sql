SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:      System
-- Create date: 2025-01-03
-- Description: Duplicate a conversation for sharing functionality
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_cvst_DuplicateConversation]
    @OriginalConversationUuid UNIQUEIDENTIFIER,
    @NewUserId VARCHAR(30),
    @NewConversationUuid UNIQUEIDENTIFIER OUTPUT,
    @NewConversationId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if original conversation exists
        IF NOT EXISTS (SELECT 1 FROM [dbo].[conversation] WHERE conversation_uuid = @OriginalConversationUuid)
        BEGIN
            RAISERROR('Original conversation not found', 16, 1);
            RETURN;
        END
        
        -- Get the new user's details
        DECLARE @NewDeptUnitCode VARCHAR(10);
        DECLARE @NewEmployeeType VARCHAR(10);
        
        SELECT 
            @NewDeptUnitCode = dept_unit_code,
            @NewEmployeeType = employee_type
        FROM [dbo].[acl_user_details]
        WHERE username = @NewUserId;
        
        -- Generate new conversation UUID
        SET @NewConversationUuid = NEWID();
        
        -- Create the duplicate conversation
        INSERT INTO [dbo].[conversation] (
            conversation_uuid,
            ip_address,
            ssoid,
            dept_unit_code,
            employee_type,
            keywords,
            create_by,
            create_dt,
            conversation_title,
            model_name,
            instructions,
            pastMessagesCount,
            maxResponseTokens,
            temperature,
            topP,
            share_id,
            is_shared,
            shared_from_uuid,
            update_dt
        )
        SELECT 
            @NewConversationUuid,              -- New UUID
            NULL,                              -- Clear IP address
            @NewUserId,                        -- New user
            @NewDeptUnitCode,                  -- New user's department
            @NewEmployeeType,                  -- New user's employee type
            keywords,
            @NewUserId,                        -- Created by new user
            GETDATE(),                         -- Current timestamp
            conversation_title,                -- Keep encrypted title
            model_name,
            instructions,
            pastMessagesCount,
            maxResponseTokens,
            temperature,
            topP,
            NULL,                              -- No share_id for duplicated conversation
            0,                                 -- Not shared yet
            @OriginalConversationUuid,         -- Reference to original
            GETDATE()                          -- Set update_dt to current timestamp
        FROM [dbo].[conversation]
        WHERE conversation_uuid = @OriginalConversationUuid;
        
        -- Get the new conversation ID
        SET @NewConversationId = SCOPE_IDENTITY();
        
        -- Create a mapping table for old to new message UUIDs
        CREATE TABLE #MessageMapping (
            old_message_id INT,
            old_message_uuid UNIQUEIDENTIFIER,
            new_message_id INT,
            new_message_uuid UNIQUEIDENTIFIER
        );
        
        -- Duplicate all messages (excluding soft-deleted ones)
        INSERT INTO [dbo].[message] (
            message_uuid,
            conversation_id,
            temperature,
            instance_name,
            model_name,
            sender,
            token_spent,
            reaction,
            create_by,
            create_dt,
            received_at,
            dialog_id,
            last_prompt,
            is_deleted,
            used_mention
        )
        OUTPUT 
            inserted.message_id,
            inserted.message_uuid
        INTO #MessageMapping(new_message_id, new_message_uuid)
        SELECT 
            NEWID(),                           -- New message UUID
            @NewConversationId,                -- New conversation ID
            temperature,
            instance_name,
            model_name,
            sender,
            token_spent,
            NULL,                              -- Clear reaction
            @NewUserId,                        -- Created by new user
            create_dt,                         -- Keep original timestamp to preserve order
            received_at,
            dialog_id,
            last_prompt,
            0,                                 -- Set is_deleted to false for all duplicated messages
            used_mention                       -- Preserve used_mention value
        FROM [dbo].[message]
        WHERE conversation_id = (
            SELECT conversation_id 
            FROM [dbo].[conversation] 
            WHERE conversation_uuid = @OriginalConversationUuid
        )
        AND COALESCE(is_deleted, 0) = 0       -- Filter out soft-deleted messages
        ORDER BY message_id;
        
        -- Update the mapping with old message IDs
        UPDATE m
        SET m.old_message_id = orig.message_id,
            m.old_message_uuid = orig.message_uuid
        FROM #MessageMapping m
        INNER JOIN (
            SELECT 
                message_id,
                message_uuid,
                ROW_NUMBER() OVER (ORDER BY message_id) as rn
            FROM [dbo].[message]
            WHERE conversation_id = (
                SELECT conversation_id 
                FROM [dbo].[conversation] 
                WHERE conversation_uuid = @OriginalConversationUuid
            )
            AND COALESCE(is_deleted, 0) = 0   -- Filter out soft-deleted messages in mapping too
        ) orig ON m.new_message_id = (
            SELECT MIN(new_message_id) - 1 + orig.rn 
            FROM #MessageMapping
        );
        
        -- Duplicate prompts
        INSERT INTO [dbo].[prompt] (
            message_id,
            prompt_data,
            prompt_order
        )
        SELECT 
            m.new_message_id,
            p.prompt_data,
            p.prompt_order
        FROM [dbo].[prompt] p
        INNER JOIN #MessageMapping m ON p.message_id = m.old_message_id;
        
        -- Duplicate message sources
        INSERT INTO [dbo].[message_source] (
            message_id,
            title_encrypted,
            link_encrypted,
            snippet_encrypted,
            create_dt
        )
        SELECT 
            m.new_message_id,
            ms.title_encrypted,
            ms.link_encrypted,
            ms.snippet_encrypted,
            GETDATE()
        FROM [dbo].[message_source] ms
        INNER JOIN #MessageMapping m ON ms.message_id = m.old_message_id;
        
        -- Clean up temp table
        DROP TABLE #MessageMapping;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Log error
        INSERT INTO [dbo].[sys_sql_error] (
            sp_name,
            error_num,
            error_line,
            error_msg,
            error_severity,
            error_state,
            create_dt
        )
        VALUES (
            'sp_cvst_DuplicateConversation',
            ERROR_NUMBER(),
            ERROR_LINE(),
            ERROR_MESSAGE(),
            ERROR_SEVERITY(),
            ERROR_STATE(),
            GETDATE()
        );
        
        THROW;
    END CATCH
END
GO