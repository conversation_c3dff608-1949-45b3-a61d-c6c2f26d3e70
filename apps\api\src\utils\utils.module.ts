import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { OcrService } from './ocr.service';
import { KeywordExtractionService } from './keyword-extraction.service';
import { SearchContextService } from './search-context.service';
import { FileProcessingService } from './file-processing.service';
import { ImageStorageService } from './image-storage.service';
import { LoggingSanitizationService } from './logging-sanitization.service';
import { LlmModule } from '../llm/llm.module'; // Import LlmModule for LlmConfigService

@Module({
  imports: [ConfigModule, forwardRef(() => LlmModule)], // Import LlmModule for KeywordExtractionService, use forwardRef to avoid circular dependency
  providers: [
    OcrService,
    KeywordExtractionService,
    SearchContextService,
    FileProcessingService,
    ImageStorageService,
    LoggingSanitizationService,
  ],
  exports: [
    OcrService,
    KeywordExtractionService,
    SearchContextService,
    FileProcessingService,
    ImageStorageService,
    LoggingSanitizationService,
  ], // Export new services
})
export class UtilsModule {}
