import { redirect } from 'next/navigation';

export default async function CategoryPage({
  params,
}: {
  params: Promise<{ category: string | string[] }>;
}) {
  // Await the params Promise as required by Next.js PageProps type
  const { category } = await params;

  const categorySlug = Array.isArray(category) ? category[0] : category;

  const target = categorySlug
    ? `/models?category=${encodeURIComponent(categorySlug)}`
    : '/models';

  redirect(target);
}
