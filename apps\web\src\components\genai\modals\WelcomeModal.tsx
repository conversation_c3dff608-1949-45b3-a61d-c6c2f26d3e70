'use client';

import React, { useEffect, useState } from 'react';
import {
  Modal,
  SwipeableDrawer,
  useTheme,
  useMediaQuery,
  Box,
  Typography,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Fade,
  Fab,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SyncIcon from '@mui/icons-material/Sync';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import CollectionsBookmarkIcon from '@mui/icons-material/CollectionsBookmark';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import MicIcon from '@mui/icons-material/Mic';
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
import ShareIcon from '@mui/icons-material/Share';
import TuneIcon from '@mui/icons-material/Tune';
import { useSession } from 'next-auth/react';
import { useGetWelcomeStatusQuery, useMarkWelcomeSeenMutation } from '@/lib/store/apiSlice';

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '90%',
  maxWidth: '800px',
  maxHeight: '90vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px',
  overflow: 'hidden',
  outline: 'none',
};

const WelcomeModal: React.FC = () => {
  const { data: session } = useSession();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [showModal, setShowModal] = useState(false);

  const { data: welcomeStatus } = useGetWelcomeStatusQuery(undefined, {
    skip: !session,
  });
  
  const [markWelcomeSeen] = useMarkWelcomeSeenMutation();

  // Show modal if user hasn't been notified yet
  useEffect(() => {
    if (session && welcomeStatus && !welcomeStatus.notified) {
      setShowModal(true);
    }
  }, [session, welcomeStatus]);

  const handleClose = async () => {
    setShowModal(false);
    await markWelcomeSeen();
  };

  const WelcomeContent = () => (
    <>
      <Box sx={{ p: 3, flexShrink: 0 }}>
        <Typography variant="h5" gutterBottom color="primary">
          Welcome to the new GenAI platform!
        </Typography>
        <Typography variant="body1" paragraph color="text.secondary">
          We're excited to introduce you to the enhanced features:
        </Typography>
      </Box>
      <Box sx={{ px: 3, pb: 1, overflowY: 'auto', flexGrow: 1 }}>
        <List dense>
          <ListItem>
            <ListItemIcon>
              <SyncIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Cross-Device Chat" 
              secondary="Sessions and history are now synced across all your devices." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <AutoFixHighIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Prompt Rewrite" 
              secondary="AI-powered suggestions to help users refine their prompts." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <CollectionsBookmarkIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Prompt Gallery" 
              secondary="A library of pre-built, effective prompts for common tasks." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <AccountTreeIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Multi-Model Chat" 
              secondary="The ability to switch between different large language models." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <FileUploadIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Universal File Upload" 
              secondary="Users can now upload documents for analysis with any model." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <MicIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Voice Input" 
              secondary="Support for voice-to-text dictation." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <TravelExploreIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Web Search Integration" 
              secondary="Allows models to access real-time information from the internet." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <ShareIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Chat Sharing" 
              secondary="Easily share and collaborate on chat sessions." 
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <TuneIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Model Parameters" 
              secondary="Advanced controls for users to fine-tune model outputs." 
            />
          </ListItem>
        </List>
      </Box>
      <Box sx={{ p: 3, flexShrink: 0, borderTop: 1, borderColor: 'divider' }}>
        <Button
          variant="contained"
          onClick={handleClose}
          fullWidth
          sx={{ py: 1.5 }}
        >
          Get Started
        </Button>
      </Box>
    </>
  );

  if (!session || !showModal) {
    return null;
  }

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={showModal}
        onClose={handleClose}
        onOpen={() => {}}
        disableSwipeToOpen
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            height: 'auto',
            maxHeight: '90vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          },
        }}
      >
        <Box sx={{ p: 2, flexShrink: 0 }}>
          <Fab
            color="primary"
            aria-label="close"
            onClick={handleClose}
            size="small"
            sx={{ 
              position: 'absolute', 
              top: 8, 
              right: 8,
              minHeight: 40,
              height: 40,
              width: 40,
            }}
          >
            <CloseIcon fontSize="small" />
          </Fab>
        </Box>
        <WelcomeContent />
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={showModal}
      onClose={handleClose}
      closeAfterTransition
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Fade in={showModal}>
        <Box sx={modalStyle}>
          <Box sx={{ position: 'relative' }}>
            <Fab
              color="primary"
              aria-label="close"
              onClick={handleClose}
              size="small"
              sx={{ 
                position: 'absolute', 
                top: 8, 
                right: 8,
                zIndex: 1,
                minHeight: 40,
                height: 40,
                width: 40,
              }}
            >
              <CloseIcon fontSize="small" />
            </Fab>
          </Box>
          <WelcomeContent />
        </Box>
      </Fade>
    </Modal>
  );
};

export default WelcomeModal;