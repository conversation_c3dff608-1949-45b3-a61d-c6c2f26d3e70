{"name": "@hkbu-genai-platform/api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "postinstall": ""}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@azure/cognitiveservices-computervision": "^8.2.0", "@azure/core-auth": "^1.9.0", "@azure/identity": "^4.0.0", "@azure/keyvault-secrets": "^4.8.0", "@azure/ms-rest-js": "^2.7.0", "@azure/openai": "^2.0.0", "@fastify/cors": "^11.0.1", "@fastify/multipart": "^9.0.3", "@google-cloud/speech": "^7.0.1", "@google-cloud/vertexai": "^1.10.0", "@hkbu-genai-platform/database": "workspace:*", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.66", "@langchain/google-genai": "^0.2.3", "@langchain/google-vertexai": "^0.2.13", "@langchain/openai": "^0.5.18", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-fastify": "^11.0.13", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.1", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.5.0", "@types/lodash": "^4.17.16", "@types/node-fetch": "^2", "@types/passport-jwt": "^4.0.1", "@types/uuid": "^10.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "fastify": "^5.3.0", "https-proxy-agent": "^7.0.6", "ioredis": "^5.3.2", "langchain": "^0.3.24", "lodash": "^4.17.21", "mammoth": "^1.7.0", "moment": "^2.30.1", "nestjs-throttler-storage-redis": "^0.4.4", "node-fetch": "^2.7.0", "node-pptx-parser": "^1.0.1", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "google-auth-library": "^9.15.1", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}