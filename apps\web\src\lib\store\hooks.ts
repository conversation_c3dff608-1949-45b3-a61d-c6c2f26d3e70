import { useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from './store';
import { selectActiveStreamingConversation, selectConversationId } from './chatSlice';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();

// Custom hook for streaming status computation
export const useStreamingStatus = () => {
  const activeStreamingConversationId = useAppSelector(selectActiveStreamingConversation);
  const conversationId = useAppSelector(selectConversationId);
  
  const isStreamingActiveForThisConversation = activeStreamingConversationId === conversationId;
  
  return {
    activeStreamingConversationId,
    isStreamingActiveForThisConversation,
  };
};
