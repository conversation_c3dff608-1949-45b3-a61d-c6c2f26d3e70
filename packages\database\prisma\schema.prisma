// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["referentialIntegrity"]
  output          = "./generated/client" // Changed output path
}

datasource db {
  provider          = "sqlserver"
  url               = env("DATABASE_URL")
  //shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model acl_other_user {
  user_id        String?   @db.VarChar(20)
  salu_code      String?   @db.NVarChar(20)
  fam_name       String?   @db.NVarChar(40)
  oth_name       String?   @db.NVarChar(100)
  dept_unit_code String?   @db.VarChar(10)
  email          String?   @db.VarChar(60)
  username       String    @id @db.VarChar(30)
  staff_std_type String?   @db.VarChar(10)
  is_inactive    Int?      @db.TinyInt
  ip_range       String?   @db.VarChar(255)
  create_by      String    @db.VarChar(20)
  create_dt      DateTime  @db.DateTime
  update_by      String?   @db.VarChar(20)
  update_dt      DateTime? @db.DateTime
}

model acl_user {
  user_id        String?   @db.VarChar(20)
  salu_code      String?   @db.NVarChar(20)
  fam_name       String?   @db.NVarChar(40)
  oth_name       String?   @db.NVarChar(100)
  dept_unit_code String?   @db.VarChar(10)
  email          String?   @db.VarChar(60)
  username       String    @id @db.VarChar(30)
  staff_std_type String?   @db.VarChar(10)
  is_inactive    Int?      @db.TinyInt
  ip_range       String?   @db.VarChar(255)
  create_by      String    @db.VarChar(20)
  create_dt      DateTime  @db.DateTime
  update_by      String?   @db.VarChar(20)
  update_dt      DateTime? @db.DateTime

  // Added relations
  details         acl_user_details?      @relation // Corrected: Removed fields/references from this side
  api_tokens      acl_user_api[] // Added opposite relation for acl_user_api
  api_keys        acl_user_api_key[]
  daily_usage     acl_user_daily_usage[]
  model_access    acl_user_model_access[]
  token_limits    acl_user_token_limit[]
  token_spent     acl_user_token_spent[]
  conversations   conversation[]
  feedbacks       feedback[]
  rate_limits     log_rate_limit[]
  notifications   notify[]
  request_quotes  request_quote[]
  tncs            tnc[]
  prompt_galleries prompt_gallery[]
}

model acl_user_api {
  user_id        String?   @db.VarChar(20) // Might be foreign key to acl_user?
  dept_unit_code String?   @db.VarChar(10)
  username       String    @id @db.VarChar(30) // Foreign key to acl_user
  staff_std_type String?   @db.VarChar(10)
  create_by      String    @db.VarChar(20)
  create_dt      DateTime  @db.DateTime
  update_by      String?   @db.VarChar(20)
  update_dt      DateTime? @db.DateTime

  // Assuming username links to acl_user
  user           acl_user @relation(fields: [username], references: [username])

  // Assuming related to api_footprint indirectly through prompts?
  // api_request_prompts  api_request_prompt[]
  // api_response_prompts api_response_prompt[]
}

model acl_user_api_key {
  id         Int       @id @default(autoincrement())
  username   String    @db.VarChar(30)
  api_key    String?   @unique @db.UniqueIdentifier
  ip_address String?   @db.VarChar(50)
  rec_status String    @db.VarChar(1)
  create_by  String    @db.VarChar(20)
  create_dt  DateTime  @db.DateTime
  update_by  String?   @db.VarChar(20)
  update_dt  DateTime? @db.DateTime

  // Added relation
  user       acl_user  @relation(fields: [username], references: [username])
}

model acl_user_daily_usage {
  id                Int       @id @default(autoincrement())
  ssoid             String    @db.VarChar(30)
  model             String    @db.NVarChar(255) // Can link to model_list.model_name ?
  daily_usage_count Int       @db.Int
  usage_date        DateTime  @db.Date
  create_by         String    @db.NVarChar(50)
  create_dt         DateTime  @db.DateTime
  update_by         String?   @db.NVarChar(50)
  update_dt         DateTime? @db.DateTime

  // Added relation
  user              acl_user  @relation(fields: [ssoid], references: [username])

  @@unique([ssoid, model, usage_date], name: "UC_ssoid_model_date")
}

model acl_user_details {
  username       String    @id @db.VarChar(30) // Foreign key to acl_user
  dept_unit_code String?   @db.VarChar(10)
  employee_type  String?   @db.VarChar(10)
  create_by      String    @db.VarChar(20)
  create_dt      DateTime  @db.DateTime
  update_by      String?   @db.VarChar(20)
  update_dt      DateTime? @db.DateTime

  // Added relation (one-to-one)
  user           acl_user            @relation(fields: [username], references: [username]) // Corrected: Ensures fields/references are on FK side
  recent_models  user_recent_model[] // Added relation for recently used models
  tutorial_progress user_tutorial_progress? // Added relation for tutorial progress
}

model acl_user_model_access {
  id            Int       @id @default(autoincrement())
  username      String    @db.VarChar(30) // Foreign key to acl_user
  staff_std_type String?   @db.VarChar(10) // Redundant? acl_user has staff_std_type
  model_list_id Int       @db.Int // Foreign key to model_list
  api_status    String    @db.VarChar(1)
  rec_status    String    @db.VarChar(1)
  expiry_date   DateTime? @db.DateTime
  create_by     String    @db.VarChar(20)
  create_dt     DateTime  @db.DateTime
  update_by     String?   @db.VarChar(20)
  update_dt     DateTime? @db.DateTime

  // Added relations
  user          acl_user  @relation(fields: [username], references: [username])
  model         model_list @relation(fields: [model_list_id], references: [id])
}

model acl_user_token_limit {
  username            String    @db.VarChar(30) // Foreign key to acl_user
  model_list_id       Int       @db.Int // Foreign key to model_list
  monthly_token_limit Int?      @db.Int
  create_by           String    @db.VarChar(20)
  create_dt           DateTime  @db.DateTime
  update_by           String?   @db.VarChar(20)
  update_dt           DateTime? @db.DateTime

  // Added relations
  user                acl_user   @relation(fields: [username], references: [username])
  model               model_list @relation(fields: [model_list_id], references: [id])

  @@id([username, model_list_id])
}

model acl_user_token_spent {
  id                    Int       @id @default(autoincrement())
  username              String?   @db.VarChar(30) // Foreign key to acl_user
  model_name            String?   @db.VarChar(30) // Can potentially link to model_list.model_name?
  token_date            DateTime  @db.Date
  is_api                Int?      @db.Int
  token_spent           Int?      @db.Int
  token_spent_user      Int?      @db.Int
  token_spent_assistant Int?      @db.Int
  last_token_spent      Int?      @db.Int
  message_count         Int?      @db.Int
  conversation_count    Int?      @db.Int
  create_by             String    @db.VarChar(20)
  create_dt             DateTime  @db.DateTime
  update_by             String?   @db.VarChar(20)
  update_dt             DateTime? @db.DateTime

  // Added relation
  user                  acl_user? @relation(fields: [username], references: [username])
}

// --- API Footprint and related tables ---
// Relations here seem complex and might require more context or DB schema details
// For now, focus on the core tables needed for the migration task.
model api_footprint {
  id              Int                   @id @default(autoincrement())
  ip_address      String?               @db.VarChar(50)
  ssoid           String?               @db.VarChar(30) // Foreign key to acl_user?
  instance_name   String?               @db.VarChar(30)
  model_name      String?               @db.VarChar(30) // Foreign key to model_list?
  method          String?               @db.VarChar(20)
  request_url     String?               @db.NVarChar(1000)
  request_header  String?               @db.NVarChar
  request_dt      DateTime?             @db.DateTime
  response_code   Int?                  @db.Int
  response_header String?               @db.NVarChar
  response_dt     DateTime?             @db.DateTime
  token_spent     Int?                  @db.Int
  created_dt      DateTime              @db.DateTime
  updated_dt      DateTime?             @db.DateTime
  request_prompts api_request_prompt[] // Changed name for clarity
  response_prompts api_response_prompt[] // Changed name for clarity
}

model api_request_prompt {
  id               Int           @id @default(autoincrement())
  // message_id    Int           // Meaning unclear, removed for now unless needed
  prompt_data      Bytes?        @db.VarBinary
  prompt_order     Int
  api_footprint_id Int           // Foreign key to api_footprint
  api_footprint    api_footprint @relation(fields: [api_footprint_id], references: [id])

  @@index([api_footprint_id])
}

model api_response_prompt {
  id               Int           @id @default(autoincrement())
  // message_id    Int           // Meaning unclear, removed for now unless needed
  prompt_data      Bytes?        @db.VarBinary
  prompt_order     Int
  api_footprint_id Int           // Foreign key to api_footprint
  api_footprint    api_footprint @relation(fields: [api_footprint_id], references: [id])

  @@index([api_footprint_id])
}
// --- End API Footprint ---


model conversation {
  conversation_id   Int        @id @default(autoincrement())
  conversation_uuid String?    @unique @db.UniqueIdentifier
  ip_address        String?    @db.VarChar(50)
  ssoid             String?    @db.VarChar(30) // Foreign key to acl_user
  dept_unit_code    String?    @db.VarChar(10)
  employee_type     String?    @db.VarChar(10) // Redundant? acl_user has staff_std_type
  keywords          String?    @db.NVarChar
  create_by         String     @db.VarChar(20)
  create_dt         DateTime   @db.DateTime
  update_by         String?    @db.VarChar(20)
  update_dt         DateTime?  @db.DateTime
  conversation_title Bytes?    @db.VarBinary(Max) // Re-added encrypted title
  model_name        String?    @db.VarChar(30)    // Re-added model name used
  // Removed plain text title field

  // Added Model Parameters
  instructions      String?    @db.NVarChar(Max) // System prompt/instructions
  pastMessagesCount Int?       @db.Int           // Number of past messages to include
  maxResponseTokens Int?       @db.Int           // Max tokens for the response
  temperature       Float?     @db.Float         // LLM temperature setting
  topP              Float?     @db.Float         // LLM top_p setting

  // Sharing fields
  share_id          String?    @unique @db.UniqueIdentifier // Unique ID for share link
  is_shared         Boolean    @default(false)               // Has this conversation been shared
  shared_from_uuid  String?    @db.UniqueIdentifier          // Original conversation UUID if duplicated
  delete_dt         DateTime?  @db.DateTime
 
   // Added relations
   user              acl_user?  @relation(fields: [ssoid], references: [username])
  messages          message[]
}

model edir_staff {
  emp_id         Int       @db.Int
  salu_code      String?   @db.VarChar(15)
  name           String?   @db.NVarChar(100)
  pref_salu      String?   @db.VarChar(15)
  pref_chi_name  String?   @db.NVarChar(20)
  dept_unit_code String    @db.VarChar(4)
  dept_unit_desc String?   @db.NVarChar(80)
  functit_name   String?   @db.NVarChar(50)
  email_staff    String?   @db.VarChar(25)
  teach_code     String?   @db.VarChar(5)
  teach_ind      String?   @db.VarChar(1)
  staff_status   String?   @db.VarChar(10)
  pref_fam_name  String?   @db.NVarChar(28)
  pref_oth_name  String?   @db.NVarChar(100)
  div_code       String?   @db.VarChar(4)
  report_unit    String?   @db.VarChar(4)
  dean_head_ind  String?   @db.VarChar(4)
  create_dt      DateTime? @db.DateTimeOffset

  @@id([emp_id, dept_unit_code])
}

model feedback {
  feedback_id         Int       @id @default(autoincrement())
  ssoid               String    @db.VarChar(30) // Foreign key to acl_user
  ip_address          String?   @db.VarChar(50)
  message             String?   @db.NVarChar
  ack_email_sent      String?   @db.VarChar(1)
  ack_email_sent_date DateTime? @db.DateTime
  create_by           String    @db.VarChar(20)
  create_dt           DateTime  @db.DateTime
  update_by           String?   @db.VarChar(20)
  update_dt           DateTime? @db.DateTime

  // Added relation
  user                acl_user  @relation(fields: [ssoid], references: [username])
}

model log_rate_limit {
  id         Int       @id @default(autoincrement())
  username   String?   @db.VarChar(30) // Foreign key to acl_user
  api_key    String?   @db.UniqueIdentifier // Can relate to acl_user_api_key.api_key?
  identifier String?   @db.VarChar(50)
  model_name String?   @db.VarChar(20) // Can relate to model_list.model_name ?
  limit      Int?      @db.Int
  remaining  Int?      @db.Int
  create_dt  DateTime  @db.DateTime

  // Added relation
  user       acl_user? @relation(fields: [username], references: [username])
}

model message {
  message_id      Int        @id @default(autoincrement())
  message_uuid    String?    @unique @db.UniqueIdentifier
  conversation_id Int        // Foreign key to conversation
  temperature     Decimal?   @db.Decimal(2, 1)
  instance_name   String?    @db.VarChar(100) // Reverted from MAX
  model_name      String?    @db.VarChar(100) // Reverted from MAX
  sender          String?    @db.VarChar(20)
  token_spent     Int?       @db.Int
  // Removed content field
  reaction        String?    @db.VarChar(1)
  create_by       String     @db.VarChar(100) // Reverted from MAX
  create_dt       DateTime   @db.DateTime
  update_by       String?    @db.VarChar(20)
  update_dt       DateTime?  @db.DateTime
  received_at     DateTime?  @db.DateTime
  dialog_id       String?    @db.VarChar(255) // Reverted from MAX
  last_prompt     Bytes?     @db.VarBinary(Max) // Added back for old structure compatibility
  is_deleted      Boolean    @default(false) @db.Bit // Added for soft delete functionality
  used_mention    Boolean?   @db.Bit // Track if user explicitly used @mention for this message

  // Added relations
  conversation    conversation @relation(fields: [conversation_id], references: [conversation_id])
  prompts         prompt[] // Added back prompts relation

  // Add relation to MessageSource
  sources         MessageSource[] // One message can have multiple sources

  @@map("message") // Explicitly map model name to table name
}

model prompt {
  id          Int      @id @default(autoincrement())
  message_id  Int      // Foreign key to message
  prompt_data Bytes?   @db.VarBinary(Max) // Changed to VarBinary(Max)
  prompt_order Int
  message     message  @relation(fields: [message_id], references: [message_id])

  @@index([message_id])
}

// --- Start: Added MessageSource Model ---
model MessageSource {
  messageSourceId   Int       @id @default(autoincrement()) @map("message_source_id")
  messageId         Int       @map("message_id")
  titleEncrypted    Bytes?    @map("title_encrypted") @db.VarBinary(Max)
  linkEncrypted     Bytes?    @map("link_encrypted") @db.VarBinary(Max)
  snippetEncrypted  Bytes?    @map("snippet_encrypted") @db.VarBinary(Max)
  createDt          DateTime  @default(now()) @map("create_dt") @db.DateTime

  // Relation to Message
  message           message   @relation(fields: [messageId], references: [message_id]) // Use lowercase 'message' model name

  @@index([messageId])
  @@map("message_source") // Map model name to table name
}
// --- End: Added MessageSource Model ---

model model_list {
  id                      Int                     @id @default(autoincrement())
  display_name            String?                 @db.VarChar(50)
  deployment_name         String?                 @unique @db.VarChar(30)
  model_name              String?                 @db.VarChar(30)
  model_type              String?                 @db.VarChar(20)
  api_version             String?                 @db.VarChar(20)
  whitelist               String?                 @db.VarChar(500)
  stf_monthly_token_limit Int?                    @db.Int
  stf_threshold           Int?                    @db.Int
  stf_threshold_daily     Int?                    @db.Int
  std_monthly_token_limit Int?                    @db.Int
  std_threshold           Int?                    @db.Int
  std_threshold_daily     Int?                    @db.Int
  max_token_prompt        Int?                    @db.Int
  seq                     Int?                    @db.Int
  api_status              String                  @db.VarChar(1)
  rec_status              String                  @db.VarChar(1)
  availability_status     String                  @default("A") @db.VarChar(1)
  create_by               String                  @db.VarChar(20)
  create_dt               DateTime                @db.DateTime
  update_by               String?                 @db.VarChar(20)
  update_dt               DateTime?               @db.DateTime
  category                String?                 @db.VarChar(50) // Added for model categorization
  supports_vision         Boolean                 @default(false) // Added to indicate vision capabilities

  // Added relations
  acl_user_access         acl_user_model_access[]
  token_limits            acl_user_token_limit[]
  recent_usages           user_recent_model[] // Added relation for recently used models
}

model mtr_dept {
  dept_unit_code   String    @id @db.NVarChar(50)
  parent_unit_code String?   @db.NVarChar(50)
  dept_unit_desc   String?   @db.NVarChar(200)
  dept_unit_chi    String?   @db.NVarChar(100)
  unit_stu         String?   @db.VarChar(1)
  unit_nature      String?   @db.VarChar(30)
  report_unit_code String?   @db.VarChar(4)
  pgm_code_ind     String?   @db.VarChar(1)
  email            String?   @db.VarChar(200)
  source           String?   @db.VarChar(10)
  create_by        String    @db.VarChar(50)
  create_dt        DateTime  @db.DateTime
  update_by        String?   @db.VarChar(50)
  update_dt        DateTime? @db.DateTime

  // Potential relations (assuming dept_unit_code links)
  // acl_users         acl_user[] @relation(fields: [dept_unit_code], references: [dept_unit_code]) ? Requires index on acl_user.dept_unit_code
  // acl_user_details  acl_user_details[] @relation(fields: [dept_unit_code], references: [dept_unit_code]) ? Requires index
}

model mtr_mapping_temperature {
  dept_unit_code String    @id @db.VarChar(10)
  temperature    Decimal   @db.Decimal(1, 1)
  rec_status     String    @db.VarChar(1)
  create_by      String    @db.VarChar(20)
  create_dt      DateTime  @db.DateTime
  update_by      String?   @db.VarChar(20)
  update_dt      DateTime? @db.DateTime

  // Potential relations
  // acl_users      acl_user[] @relation(fields: [dept_unit_code], references: [dept_unit_code]) ? Requires index
}

model notify {
  notify_id Int       @id @default(autoincrement())
  ssoid     String    @db.VarChar(30) // Foreign key to acl_user
  notified  String?   @db.VarChar(1)
  create_by String    @db.VarChar(20)
  create_dt DateTime  @db.DateTime
  update_by String?   @db.VarChar(20)
  update_dt DateTime? @db.DateTime

  // Added relation
  user      acl_user  @relation(fields: [ssoid], references: [username])
}

model request_quote {
  request_quote_id    Int       @id @default(autoincrement())
  ssoid               String    @db.VarChar(30) // Foreign key to acl_user
  ip_address          String?   @db.VarChar(50)
  model               String?   @db.NVarChar // Can relate to model_list.model_name ?
  quote               String?   @db.NVarChar
  duration            String?   @db.NVarChar
  reason              String?   @db.NVarChar
  ack_email_sent      String?   @db.VarChar(1)
  ack_email_sent_date DateTime? @db.DateTime
  create_by           String    @db.VarChar(20)
  create_dt           DateTime  @db.DateTime
  update_by           String?   @db.VarChar(20)
  update_dt           DateTime? @db.DateTime

  // Added relation
  user                acl_user  @relation(fields: [ssoid], references: [username])
}

model sys_job {
  job_id           Int             @id @default(autoincrement())
  job_type_id      Int             // Foreign key to sys_job_type
  start_dt         DateTime?       @db.DateTime
  process_start_dt DateTime?       @db.DateTime
  process_end_dt   DateTime?       @db.DateTime
  job_status_code  String?         @db.VarChar(20) // Foreign key to sys_job_status
  input_parameter  String?         @db.NVarChar
  output_result    String?         @db.NVarChar
  remarks          String?         @db.NVarChar
  create_by        String          @db.VarChar(20)
  create_dt        DateTime        @db.DateTime
  update_by        String?         @db.VarChar(20)
  update_dt        DateTime?       @db.DateTime

  // Added relations
  job_type         sys_job_type    @relation(fields: [job_type_id], references: [job_type_id])
  job_status       sys_job_status? @relation(fields: [job_status_code], references: [job_status_code])
}

model sys_job_status {
  job_status_code String    @id @db.VarChar(20)
  name            String?   @db.NVarChar(100)
  description     String?   @db.NVarChar(100)
  create_by       String    @db.VarChar(20)
  create_dt       DateTime  @db.DateTime
  update_by       String?   @db.VarChar(20)
  update_dt       DateTime? @db.DateTime

  // Added relation
  jobs            sys_job[]
}

model sys_job_type {
  job_type_id            Int        @id @db.Int
  description            String?    @db.NVarChar(100)
  library                String?    @db.VarChar(255)
  method                 String?    @db.VarChar(255)
  is_daily_job           Int?       @db.TinyInt
  daily_job_execution_time DateTime?  @db.Time // Changed to DateTime for Time type
  create_by              String     @db.VarChar(20)
  create_dt              DateTime   @db.DateTime
  update_by              String?    @db.VarChar(20)
  update_dt              DateTime?  @db.DateTime

  // Added relation
  jobs                   sys_job[]
}

model sys_sql_error {
  id             BigInt   @id @default(autoincrement())
  sp_name        String?  @db.VarChar(100)
  error_num      Int?     @db.Int
  error_line     Int?     @db.Int
  error_msg      String?  @db.NVarChar(200)
  error_severity Int?     @db.Int
  error_state    Int?     @db.Int
  create_dt      DateTime @db.DateTime
}

model tnc {
  tnc_id    Int       @id @default(autoincrement())
  ssoid     String    @db.VarChar(30) // Foreign key to acl_user
  agreed    String?   @db.VarChar(1)
  create_by String    @db.VarChar(20)
  create_dt DateTime  @db.DateTime
  update_by String?   @db.VarChar(20)
  update_dt DateTime? @db.DateTime

  // Added relation
  user      acl_user  @relation(fields: [ssoid], references: [username])
}

model user_recent_model {
  id         Int      @id @default(autoincrement())
  userId     String   @db.VarChar(30) // Foreign key to acl_user_details.username
  modelId    Int      // Foreign key to model_list.id
  lastUsedAt DateTime @db.DateTime @default(now()) @updatedAt

  user  acl_user_details @relation(fields: [userId], references: [username])
  model model_list       @relation(fields: [modelId], references: [id])

  @@unique([userId, modelId]) // Ensure one entry per user/model
  @@index([userId, lastUsedAt]) // Index for efficient querying
}

model task {
  id                  Int                   @id @default(autoincrement())
  name                String                @db.NVarChar(100)
  create_dt           DateTime              @default(now()) @db.DateTime
  update_dt           DateTime              @updatedAt @db.DateTime
  prompt_gallery_task prompt_gallery_task[]
}

model prompt_gallery {
  id                  Int                   @id @default(autoincrement())
  system_instruction  String?               @db.NVarChar(Max)
  prompt_content      String                @db.NVarChar(Max)
  user_id             String?               @db.VarChar(30)
  is_default          Boolean               @default(false)
  create_dt           DateTime              @default(now()) @db.DateTime
  update_dt           DateTime              @updatedAt @db.DateTime
  user                acl_user?             @relation(fields: [user_id], references: [username])
  prompt_gallery_task prompt_gallery_task[]
}

model prompt_gallery_task {
  prompt_gallery_id Int
  task_id           Int
  prompt_gallery    prompt_gallery @relation(fields: [prompt_gallery_id], references: [id])
  task              task           @relation(fields: [task_id], references: [id])

  @@id([prompt_gallery_id, task_id])
}

model user_tutorial_progress {
  id        Int      @id @default(autoincrement())
  username  String   @unique @db.VarChar(30)
  completed Boolean  @default(false)
  completed_tours String? @db.NVarChar(Max)
  user      acl_user_details @relation(fields: [username], references: [username])
  create_dt DateTime @default(now()) @db.DateTime
  update_dt DateTime @updatedAt @db.DateTime
}
