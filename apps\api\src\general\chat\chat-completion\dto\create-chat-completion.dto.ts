import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsBoolean,
  IsObject,
  ValidateNested,
  IsArray,
  IsNumber,
  Min,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MessageContent } from './create-chat-completion-rest.dto';

// Simple message structure for vision prompts
class SimpleMessageDto {
  @ApiProperty({
    description: 'The role of the message (user, assistant, system)',
  })
  @IsString()
  role!: string;

  @ApiProperty({
    description: 'The content of the message (text or vision content)',
  })
  content!: MessageContent;
}

// Nested DTO for file information
class UploadedFileDto {
  @ApiProperty({ description: 'Original filename of the uploaded file.' })
  @IsString()
  filename!: string;

  @ApiProperty({ description: 'MIME type of the uploaded file.' })
  @IsString()
  mimeType!: string;

  @ApiProperty({ description: 'Base64 encoded content of the file.' })
  @IsString()
  content!: string; // Base64 encoded string
}

// DTO for creating a chat completion request
export class CreateChatCompletionDto {
  @ApiPropertyOptional({
    description:
      'UUID of the existing chat session. If omitted, a new session will be created.',
    format: 'uuid',
  })
  @IsUUID()
  @IsOptional()
  chat_session_id?: string; // Renamed from conversation_uuid

  @ApiProperty({
    description: 'The user prompt to process.',
    type: String,
  })
  @IsString()
  prompt!: string; // Replaced messages array with a single prompt string

  @ApiProperty({ description: 'ID of the model to use.' })
  @IsString()
  model!: string; // e.g., 'gpt-4', 'gpt-3.5-turbo'

  @ApiPropertyOptional({
    description:
      'Controls randomness: lowering results in less random completions.',
    minimum: 0,
    maximum: 2,
    default: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  temperature?: number = 1;

  // Add other common OpenAI parameters as needed
  // e.g., top_p, n, max_tokens, presence_penalty, frequency_penalty, logit_bias, user, stop

  @ApiPropertyOptional({
    description:
      'If set, partial message deltas will be sent, like in ChatGPT. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a `data: [DONE]` message.',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  stream?: boolean; // Default removed, backend service will handle default behavior

  @ApiPropertyOptional({
    description:
      'Flag to indicate whether Google Search should be used to augment the context (if available and configured).',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  useGoogle?: boolean = false; // Added optional useGoogle flag

  @ApiPropertyOptional({
    description: 'Array of uploaded files associated with the prompt.',
    type: [UploadedFileDto],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UploadedFileDto)
  files?: UploadedFileDto[];

  @ApiPropertyOptional({
    description:
      'Optional messages array for vision prompts. When provided, takes precedence over prompt field.',
    type: [SimpleMessageDto],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SimpleMessageDto)
  messages?: SimpleMessageDto[];

  @ApiPropertyOptional({
    description:
      'Flag to indicate this is a regeneration request. When true, the last assistant message will be replaced.',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isRegeneration?: boolean = false;

  @ApiPropertyOptional({
    description:
      'Flag to indicate whether the user explicitly used @mention to select the model for this message.',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  usedMention?: boolean = false;

  @ApiPropertyOptional({
    description: 'System instructions to prepend to the conversation.',
    type: String,
  })
  @IsString()
  @IsOptional()
  instructions?: string;

  @ApiPropertyOptional({
    description:
      'Number of past messages to include in context. Default is 10.',
    minimum: 0,
    maximum: 50,
    default: 10,
  })
  @IsNumber()
  @Min(0)
  @Max(50)
  @IsOptional()
  pastMessagesCount?: number = 10;

  @ApiPropertyOptional({
    description:
      'Maximum number of tokens to generate in the response. Default is 2000.',
    minimum: 1,
    maximum: 16384,
    default: 2000,
  })
  @IsNumber()
  @Min(1)
  @Max(16384)
  @IsOptional()
  maxResponseTokens?: number = 2000;

  @ApiPropertyOptional({
    description:
      'Nucleus sampling threshold. Default is 0.6.',
    minimum: 0,
    maximum: 1,
    default: 0.6,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  topP?: number = 0.6;

  // --- Pending Parameters for New Conversations ---
  // These fields allow the frontend to send user-set parameters that should
  // be applied when creating a new conversation, overriding the defaults above

  @ApiPropertyOptional({
    description: 'Pending system instructions to apply to new conversations. Takes precedence over instructions field.',
    type: String,
  })
  @IsString()
  @IsOptional()
  pendingInstructions?: string;

  @ApiPropertyOptional({
    description: 'Pending past messages count to apply to new conversations. Takes precedence over pastMessagesCount field.',
    minimum: 0,
    maximum: 50,
  })
  @IsNumber()
  @Min(0)
  @Max(50)
  @IsOptional()
  pendingPastMessagesCount?: number;

  @ApiPropertyOptional({
    description: 'Pending max response tokens to apply to new conversations. Takes precedence over maxResponseTokens field.',
    minimum: 1,
    maximum: 16384,
  })
  @IsNumber()
  @Min(1)
  @Max(16384)
  @IsOptional()
  pendingMaxResponseTokens?: number;

  @ApiPropertyOptional({
    description: 'Pending temperature to apply to new conversations. Takes precedence over temperature field.',
    minimum: 0,
    maximum: 2,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  pendingTemperature?: number;

  @ApiPropertyOptional({
    description: 'Pending top-p value to apply to new conversations. Takes precedence over topP field.',
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  pendingTopP?: number;

}

// Response DTOs can be added later if needed for non-streaming responses
