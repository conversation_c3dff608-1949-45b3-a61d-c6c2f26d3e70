import { Source } from '@/lib/store/chatSlice';

export const parseThinkBlocks = (content: string | undefined) => {
  if (!content) {
    return { regularContent: '', thinkingContent: '' };
  }

  let regularContent = '';
  let thinkingContent = '';
  const thinkStartTag = '<think>';
  const thinkEndTag = '</think>';
  let lastIndex = 0;
  let thinkBlockIndex = content.indexOf(thinkStartTag);

  while (thinkBlockIndex !== -1) {
    regularContent += content.substring(lastIndex, thinkBlockIndex);
    const thinkEndIndex = content.indexOf(thinkEndTag, thinkBlockIndex);
    if (thinkEndIndex === -1) {
      // No closing tag found, treat the rest as regular content
      break;
    }
    thinkingContent += content.substring(thinkBlockIndex + thinkStartTag.length, thinkEndIndex);
    lastIndex = thinkEndIndex + thinkEndTag.length;
    thinkBlockIndex = content.indexOf(thinkStartTag, lastIndex);
  }

  regularContent += content.substring(lastIndex);

  return {
    regularContent: regularContent.trim(),
    thinkingContent: thinkingContent.trim(),
  };
};

export const linkifyCitations = (
  content: string,
  sources: Source[] | undefined,
): string => {
  if (!sources || sources.length === 0 || !content) return content;
  let expanded = content.replace(/\[(\d+)\s*[–-]\s*(\d+)\]/g, (_, a, b) => {
    const start = Number(a),
      end = Number(b);
    if (end < start) return _;
    const seq = Array.from({ length: end - start + 1 }, (_, i) => start + i)
      .map((n) => `[${n}]`)
      .join('-');
    return seq;
  });

  expanded = expanded.replace(/\[((?:\d+\s*,\s*)+\d+)\]/g, (_, list) => {
    return list
      .split(/\s*,\s*/)
      .map((n: string) => `[${n}]`)
      .join(', ');
  });

  const parts = expanded.split(/```[\s\S]*?```/g);
  let result = expanded;
  let offset = 0;
  parts.forEach((part) => {
    const replaced = part.replace(
      /(?<=\s|^|\(|\[)\[(\d+)\](?=$|\s|\.|,|\)|\]|!|\?)(?!\()/g,
      (m, p1) => {
        const idx = parseInt(p1, 10) - 1;
        if (idx >= 0 && idx < sources.length) {
          return `[${p1}](${sources[idx].link})`;
        }
        return m;
      },
    );
    result =
      result.substring(0, offset) +
      replaced +
      result.substring(offset + part.length);
    offset += part.length + 0;
  });
  return result;
};