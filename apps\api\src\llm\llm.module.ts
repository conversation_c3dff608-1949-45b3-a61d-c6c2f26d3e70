import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config'; // ConfigService is needed by LlmConfigService
import { PrismaModule } from '../prisma/prisma.module'; // Import PrismaModule
import { CommonModule } from '../common/common.module'; // Import CommonModule for ModelMappingService
import { UtilsModule } from '../utils/utils.module'; // Import UtilsModule for LoggingSanitizationService
import { LlmConfigService } from './llm-config.service';
import { LlmConfigController } from './llm-config.controller'; // Import the new controller
import { AzureAiserviceLlmService } from './azure-aiservice-llm.service';
import { VertexGeminiService } from './vertex-gemini.service';
import { VertexLlamaService } from './vertex-llama.service'; // Import the new Llama service

@Module({
  imports: [
    ConfigModule, // Make ConfigService available within this module
    PrismaModule, // Make PrismaService available via PrismaModule
    CommonModule, // Make ModelMappingService available
    forwardRef(() => UtilsModule), // Make LoggingSanitizationService available, use forwardRef to avoid circular dependency
  ],
  controllers: [LlmConfigController], // Add the new controller
  providers: [
    LlmConfigService,
    AzureAiserviceLlmService,
    VertexGeminiService,
    VertexLlamaService, // Add VertexLlamaService
    // Add other LLM services here
  ],
  exports: [
    LlmConfigService,
    AzureAiserviceLlmService,
    VertexGeminiService,
    VertexLlamaService, // Export VertexLlamaService
    // Export other LLM services here
  ],
})
export class LlmModule {}
