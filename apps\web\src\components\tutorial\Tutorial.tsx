'use client';

import React, { useEffect } from 'react';
import Joyride from 'react-joyride-react19-compat';
import type { Step, CallBackProps } from 'react-joyride-react19-compat';

// Define the constants directly since there seems to be an export issue with the compat version
const STATUS = {
  IDLE: 'idle',
  READY: 'ready',
  WAITING: 'waiting',
  RUNNING: 'running',
  PAUSED: 'paused',
  SKIPPED: 'skipped',
  FINISHED: 'finished',
  ERROR: 'error'
} as const;

const EVENTS = {
  TOUR_START: 'tour:start',
  STEP_BEFORE: 'step:before',
  BEACON: 'beacon',
  TOOLTIP: 'tooltip',
  STEP_AFTER: 'step:after',
  TOUR_END: 'tour:end',
  TOUR_STATUS: 'tour:status',
  TARGET_NOT_FOUND: 'error:target_not_found',
  ERROR: 'error'
} as const;
import { useTheme } from '@mui/material';
import { useMediaQuery } from '@mui/material';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import {
  startTour,
  stopTour,
  setStepIndex,
  selectTutorialRun,
  selectTutorialStepIndex,
  selectTutorialTour,
} from '@/lib/store/tutorialSlice';
import { useCompleteTutorialMutation, useGetTutorialStatusQuery, useGetWelcomeStatusQuery } from '@/lib/store/apiSlice';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';

const mainSteps: Step[] = [
  {
    target: '[data-joyride="model-selection-area"]',
    content: 'You can choose from a variety of models here.',
  },
  {
    target: '[data-joyride="new-chat-button"]',
    content: 'Click here to start a new chat.',
  },
  {
    target: '[data-joyride="prompt-gallery-button"]',
    content: 'Explore our prompt gallery to get started quickly.',
  },
  {
    target: '[data-joyride="model-gallery-button"]',
    content: 'Browse our model gallery to see all available models.',
  },
  {
    target: '[data-joyride="recent-models-section"]',
    content: 'Your recently used models will appear here.',
  },
  {
    target: '[data-joyride="history-button"]',
    content: 'Your past conversations will be saved here.',
  },
  {
    target: '[data-joyride="settings-button"]',
    content: 'Customize your experience in the settings menu.',
  },
];

const chatSteps: Step[] = [
  {
    target: '[data-joyride="chat-input-area"]',
    content: 'This is where you can type your message to the AI.',
  },
  {
    target: '[data-joyride="chat-input-area"]',
    content: 'You can also type "@" to mention a model and select it directly in the input.',
  },
  {
    target: '[data-joyride="file-upload-button"]',
    content: 'Click here to upload files for analysis.',
  },
  {
    target: '[data-joyride="prompt-rewrite-button"]',
    content: 'Click here to get AI-powered suggestions to refine your prompt.',
  },
  {
    target: '[data-joyride="model-parameters-button"]',
    content: 'Click here to fine-tune the model parameters.',
  },
  {
    target: '[data-joyride="send-button"]',
    content: 'Click here to send your message.',
  },
  {
    target: '[data-joyride="voice-input-button"]',
    content: 'You can also use your voice to chat with the AI.',
  },
  {
    target: '[data-joyride="search-button"]',
    content: 'Toggle this to enable or disable search for your query.',
  },
  {
    target: '[data-joyride="share-chat-button"]',
    content: 'You can share your conversation with others.',
  },
];

const historySteps: Step[] = [
  {
    target: '[data-joyride="history-list"]',
    content: 'This is a list of all your past conversations.',
  },
];

const settingsSteps: Step[] = [
  {
    target: '[data-joyride="appearance-button"]',
    content: 'You can customize the appearance of the platform here.',
  },
  {
    target: '[data-joyride="sign-out-button"]',
    content: 'Click here to sign out of your account.',
  },
];

const tours = {
  main: mainSteps,
  chat: chatSteps,
  history: historySteps,
  settings: settingsSteps,
};

const Tutorial: React.FC = () => {
  
  const dispatch = useAppDispatch();
  const { data: session } = useSession();
  const pathname = usePathname();
  const run = useAppSelector(selectTutorialRun);
  const stepIndex = useAppSelector(selectTutorialStepIndex);
  const tour = useAppSelector(selectTutorialTour);
  const [completeTutorial] = useCompleteTutorialMutation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [isMounted, setIsMounted] = React.useState(false);
  const [skippedTours, setSkippedTours] = React.useState<string[]>([]);

  // These hooks must be called unconditionally
  const { data: tutorialProgress, refetch: refetchTutorialStatus } = useGetTutorialStatusQuery();
  const { data: welcomeStatus } = useGetWelcomeStatusQuery(undefined, {
    skip: !session,
  });

  const chatMobileSteps: Step[] = [
  {
    target: '[data-joyride="chat-input-area"]',
    content: 'This is where you can type your message to the AI.',
  },
  {
    target: '[data-joyride="chat-input-area"]',
    content: 'You can also type "@" to mention a model and select it directly in the input.',
  },
  {
    target: '[data-joyride="file-upload-button"]',
    content: 'Click here to upload files for analysis.',
  },
  {
    target: '[data-joyride="prompt-rewrite-button"]',
    content: 'Click here to get AI-powered suggestions to refine your prompt.',
  },
  {
    target: '[data-joyride="send-button"]',
    content: 'Click here to send your message.',
  },
  {
    target: '[data-joyride="voice-input-button"]',
    content: 'You can also use your voice to chat with the AI.',
  },
  {
    target: '[data-joyride="search-button"]',
    content: 'Toggle this to enable or disable search for your query.',
  },
  {
    target: '[data-joyride="share-chat-button"]',
    content: 'You can share your conversation with others.',
  },
  {
    target: '[data-joyride="model-parameters-button"]',
    content: 'Click here to fine-tune the model parameters.',
  },
];

  const extendedTours = {
    main: mainSteps,
    chat: chatSteps,
    'chat-mobile': chatMobileSteps,
    history: historySteps,
    settings: settingsSteps,
  };

  // Setup mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Main tutorial logic effect
  useEffect(() => {

    // Determine the tour for current path first
    let tourForPath = pathname.includes('/settings')
      ? 'settings'
      : pathname.includes('/chat/history')
      ? 'history'
      : pathname.startsWith('/chat/')
      ? 'chat'
      : 'main';

    if (tourForPath === 'chat' && isMobile) {
      tourForPath = 'chat-mobile';
    }


    // Get current state values to avoid dependency issues
    const currentRun = run;
    const currentTour = tour;
    
    // Only prevent starting if the SAME tour is already running
    if (currentRun && currentTour === tourForPath) {
      return;
    }

    // If a different tour is running, stop it first
    if (currentRun && currentTour !== tourForPath) {
      dispatch(stopTour());
    }

    if (session && tutorialProgress && welcomeStatus) {
      // Don't start tutorial if welcome modal should be shown
      if (!welcomeStatus.notified) {
        return;
      }

      
      // Check if tour was skipped in this session
      if (skippedTours.includes(tourForPath)) {
        return;
      }
      
      if (!tutorialProgress.completed_tours?.includes(tourForPath)) {
        
        // Note: No need to call stopTour() here as it's handled above if needed
        
        const currentSteps = extendedTours[tourForPath as keyof typeof extendedTours];
        
        const checkElements = () => {
          const missingElements: string[] = [];
          const foundElements: string[] = [];
          
          currentSteps.forEach(step => {
            const element = document.querySelector(step.target as string);
            if (element) {
              foundElements.push(step.target as string);
            } else {
              missingElements.push(step.target as string);
            }
          });
          
          
          return missingElements.length === 0;
        };

        if (checkElements()) {
          dispatch(startTour({ tour: tourForPath }));
        } else {
          const interval = setInterval(() => {
            if (checkElements()) {
              clearInterval(interval);
              dispatch(startTour({ tour: tourForPath }));
            }
          }, 500); // Check every 500ms

          // Timeout to prevent infinite loop
          const timeout = setTimeout(() => {
            clearInterval(interval);
          }, 10000); // Stop checking after 10 seconds

          // Cleanup interval on component unmount
          return () => {
            clearInterval(interval);
            clearTimeout(timeout);
          };
        }
      } else {
      }
    } else {
    }
  }, [session, pathname, dispatch, tutorialProgress, welcomeStatus, isMobile, run, tour, extendedTours, skippedTours]);

  const handleJoyrideCallback = async (data: CallBackProps) => {
    const { action, index, status, type } = data;
    

    // Auto-advance past beacon to show tooltip directly
    if (type === 'beacon') {
      // Force the tutorial to proceed to show the tooltip
      // We can't directly control this, but we can try setting a timeout to simulate click
      setTimeout(() => {
        // Try to find and click the beacon button to advance
        const beaconElement = document.querySelector('[data-testid="button-primary"]') || 
                             document.querySelector('.react-joyride__beacon') ||
                             document.querySelector('[aria-label="Open the dialog"]');
        if (beaconElement) {
          (beaconElement as HTMLElement).click();
        }
      }, 100);
      return;
    }

    if ([EVENTS.STEP_AFTER, EVENTS.TARGET_NOT_FOUND].includes(type)) {
      // Update step index
      dispatch(setStepIndex(index + (action === 'prev' ? -1 : 1)));
    } else if (type === EVENTS.TOUR_END) {
      // Handle actual tour completion (finished, skipped, or error)
      dispatch(stopTour());
      
      if (status === STATUS.FINISHED) {
        // User completed the tour - mark as completed
        
        // Temporarily add to skipped list to prevent restart while updating
        setSkippedTours(prev => [...prev, tour]);
        
        // Normalize chat-mobile to chat for database consistency
        const dbTourName = tour === 'chat-mobile' ? 'chat' : tour;
        
        try {
          const result = await completeTutorial({ tour: dbTourName });
          
          // Refetch tutorial status to update the local cache
          await refetchTutorialStatus();
          
          // Remove from skipped list after successful update
          setSkippedTours(prev => prev.filter(t => t !== tour));
        } catch (error) {
          console.error(`Failed to mark tour "${dbTourName}" as completed in database:`, error);
          // Remove from skipped list on error to allow retry
          setSkippedTours(prev => prev.filter(t => t !== tour));
        }
      } else if (status === STATUS.SKIPPED) {
        // User skipped the tour - just stop it, allow it to show again later (but not in this session)
        setSkippedTours(prev => {
          const updated = [...prev, tour];
          return updated;
        });
      } else {
        // Other status (error, etc.)
      }
    } else if (type === EVENTS.TOUR_STATUS) {
      // Handle status updates (running, paused, etc.) - don't stop tour
    }
  };

  // Log data changes
  useEffect(() => {
  }, [session, tutorialProgress, welcomeStatus, run, tour, stepIndex]);

  // Log when run state changes
  useEffect(() => {
    if (run) {
    }
  }, [run, tour, stepIndex]);

  // Disable tutorials completely on mobile devices
  if (isMobile) {
    return null;
  }

  if (!isMounted) {
    return null;
  }


  return (
    <Joyride
      steps={extendedTours[tour as keyof typeof extendedTours]}
      run={run}
      stepIndex={stepIndex}
      callback={handleJoyrideCallback}
      continuous
      showProgress
      showSkipButton
      spotlightClicks={true}
      disableOverlayClose={false}
      styles={{
        options: {
          zIndex: 1300, // Ensure it's above other elements but not excessively high
          arrowColor: theme.palette.background.paper,
          backgroundColor: theme.palette.background.paper,
          primaryColor: theme.palette.primary.main,
          textColor: theme.palette.text.primary,
        },
        tooltip: {
          borderRadius: theme.shape.borderRadius,
          boxShadow: theme.shadows[3],
          border: `1px solid ${theme.palette.divider}`,
        },
        buttonClose: {
          color: theme.palette.text.secondary,
          '&:hover': {
            color: theme.palette.text.primary,
          },
        },
        buttonNext: {
          backgroundColor: theme.palette.primary.main,
          borderRadius: theme.shape.borderRadius,
          color: theme.palette.primary.contrastText,
          '&:hover': {
            backgroundColor: theme.palette.primary.dark,
          },
        },
        buttonBack: {
          color: theme.palette.text.secondary,
          marginRight: 'auto',
          '&:hover': {
            color: theme.palette.text.primary,
          },
        },
        buttonSkip: {
          color: theme.palette.text.secondary,
        },
      }}
    />
  );
};

export default Tutorial;