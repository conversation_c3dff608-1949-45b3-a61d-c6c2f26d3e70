'use client';

import React, {
  Suspense,
  useEffect,
  useState,
  useRef,
  useCallback,
  KeyboardEvent,
  FormEvent,
  MouseEvent,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation'; // Added useRouter, useSearchParams
import PromptGalleryModal from '@/components/genai/modals/PromptGalleryModal'; // Import PromptGalleryModal
import PromptRewriteAssistantModal from '@/components/genai/modals/PromptRewriteAssistantModal';
import ModelParametersModal from '@/components/genai/modals/ModelParametersModal'; // Import ModelParametersModal
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Typography from '@mui/material/Typography'; // Added for LoadingIndicator
import ChatHeader from '@/components/genai/chat/ChatHeader'; // Added
import ConversationDisplay from '@/components/genai/chat/ConversationDisplay'; // Added
import ChatInputArea from '@/components/genai/chat/ChatInputArea'; // Added
import ChatErrorDisplay from '@/components/genai/chat/ChatErrorDisplay'; // Import the error display component
import { AppDispatch, RootState } from '@/lib/store/store';
import {
  selectCurrentGptModel,
  selectAvailableModels,
  setModel,
} from '@/lib/store/modelSlice'; // Selector for current model + available models + setModel action
import {
  Conversation,
  selectMessages,
  selectIsThinking,
  addUserMessage,
  triggerClearMessage,
  setChatErrorMessage,
  setIsThinking,
  selectConversationId,
  setSelectedNextModelName,
  selectPendingParameters,
  applyPendingParameters,
  clearPendingParameters,
  selectInstructions,
  selectPastMessagesCount,
  selectMaxResponseTokens,
  selectTemperature,
  selectTopP,
  selectActiveStreamingConversation,
} from '@/lib/store/chatSlice'; // Added chat actions/selectors + selectConversationId + setSelectedNextModelName + parameters + selectActiveStreamingConversation
import {
  useChatCompletionMutation,
  ChatCompletionRequest,
  useRewritePromptMutation,
  useGeneratePromptMutation,
} from '@/lib/store/apiSlice'; // Added mutation hook and request type
import { selectSupportedFileList, selectModelList } from '@/lib/store/appSlice'; // Added app selectors
import { useStreamingStatus } from '@/lib/store/hooks'; // Import streaming status hook
import { v4 as uuidv4 } from 'uuid'; // Added uuid import
import type { GptModel } from '@/lib/types/common'; // Import GptModel type
import { stripSelectedModelMentions } from '@/lib/utils/mentionUtils'; // Import mention utility

// Helper function to read file as base64 (copied from [chatId]/page.tsx)
const readFileAsBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      // Remove the data URL prefix (e.g., "data:image/png;base64,")
      resolve(base64String.split(',')[1]);
    };
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

// Component to handle search params, set Redux state, and render chat
function NewChatPageContent() {
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams(); // Get search params
  const initialQuery = searchParams.get('query') || searchParams.get('prompt');
  const initialUsedMention = searchParams.get('usedMention') === '1'; // Check if @mention was used on homepage
  // Retrieve from sessionStorage first, then fallback to query params
  const initialPromptContent =
    typeof window !== 'undefined'
      ? sessionStorage.getItem('promptContent') ||
        searchParams.get('promptContent')
      : searchParams.get('promptContent');
  const initialSystemInstruction =
    typeof window !== 'undefined'
      ? sessionStorage.getItem('systemInstruction') ||
        searchParams.get('systemInstruction')
      : searchParams.get('systemInstruction');
  const selectedModel = useSelector(selectCurrentGptModel); // Read model from Redux
  const availableModels = useSelector(selectAvailableModels); // Get available models for resolution
  const messages = useSelector(selectMessages); // messages are Conversation[]
  const isAiTyping = useSelector(selectIsThinking);
  const conversationId = useSelector(selectConversationId); // Get conversationId from Redux
  const supportedFileList = useSelector(selectSupportedFileList); // Get supported file list
  const modelList = useSelector(selectModelList); // Get full model list for vision check
  const visionModelList = modelList?.map((m: GptModel) => m.model_name) || []; // Derive vision list, add type annotation
  
  // Get parameter values from Redux
  const pendingParameters = useSelector(selectPendingParameters);
  const currentInstructions = useSelector(selectInstructions);
  const currentPastMessages = useSelector(selectPastMessagesCount);
  const currentMaxTokens = useSelector(selectMaxResponseTokens);
  const currentTemperature = useSelector(selectTemperature);
  const currentTopP = useSelector(selectTopP);
  
  // Use custom hook for streaming status
  const { activeStreamingConversationId, isStreamingActiveForThisConversation } = useStreamingStatus();
  

  // Local state for input area
  const [tempFileList, setTempFileList] = useState<File[]>([]);
  const [useGoogleEnabled, setUseGoogleEnabled] = useState<boolean>(false);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const [promptContent, setPromptContent] = useState(initialPromptContent || initialQuery || ''); // Use initialPromptContent
  const [systemInstruction, setSystemInstruction] = useState(initialSystemInstruction || ''); // New: State for system instruction
  const tempFileListRef = useRef<File[]>(tempFileList); // Ref to hold latest file list
  const [isPromptGalleryModalOpen, setIsPromptGalleryModalOpen] = useState(false); // State for Prompt Gallery Modal
  const [isPromptAssistantOpen, setIsPromptAssistantOpen] = useState(false);
  const [promptAssistantInitialContent, setPromptAssistantInitialContent] =
    useState('');
  const [isParamsModalOpen, setIsParamsModalOpen] = useState(false); // State for Model Parameters Modal

  // State and ref for selected model mentions
  const [selectedModelMentions, setSelectedModelMentions] = useState<
    Map<string, number[]>
  >(new Map());
  const clearSelectedMentionsRef = useRef<(() => void) | undefined>(undefined);

  // Use the RTK Query mutation hook for chat completion
  const [triggerChatCompletion, { isLoading: isSubmitting }] =
    useChatCompletionMutation();
  const [rewritePrompt] = useRewritePromptMutation();
  const [generatePrompt] = useGeneratePromptMutation();
  const router = useRouter(); // Get router instance

  // Local state to track if setup is complete (clearing messages)
  const [isSetupComplete, setIsSetupComplete] = useState(false);
  // Local state to track if the initial query from URL has been processed
  const [initialQueryProcessed, setInitialQueryProcessed] = useState(false);
  // Track the old conversation ID to distinguish it from new ones
  const [oldConversationId, setOldConversationId] = useState<
    string | undefined
  >(undefined);

  useEffect(() => {
    // Always clear previous chat state when mounting the new chat page
    if (!isSetupComplete) {
      // Store the old conversation ID before clearing
      setOldConversationId(conversationId);
      dispatch(triggerClearMessage());
      console.log('Dispatched triggerClearMessage'); // Debug log
      setIsSetupComplete(true); // Mark setup as complete
    }
  }, [dispatch, isSetupComplete, conversationId]); // Run once on mount (or when dispatch/isSetupComplete changes, which is fine)

  // Effect to redirect when conversationId becomes available
  useEffect(() => {
    const uuidRegex =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    const hasModelParam = searchParams.get('model') !== null;
    const hasQueryParam = searchParams.get('query') !== null;

    console.log(
      `[NewChatPage] useEffect (redirection): conversationId=${conversationId}, oldConversationId=${oldConversationId}, isValidUUID=${conversationId ? uuidRegex.test(conversationId) : 'N/A'}, hasModelParam=${hasModelParam}, hasQueryParam=${hasQueryParam}`,
    );

    // Check if this is a NEW conversation ID (different from the old one)
    const isNewConversationId =
      conversationId && conversationId !== oldConversationId;

    // Redirect if we have a valid conversationId that is either:
    // 1. A new conversation ID (different from the old one)
    // 2. OR we're not coming from model selection (original behavior)
    // But don't redirect while streaming is active to prevent interrupting responses
    if (conversationId && uuidRegex.test(conversationId) && !isStreamingActiveForThisConversation) {
      if (isNewConversationId) {
        console.log(
          `[NewChatPage] Received NEW conversationId: ${conversationId}. Redirecting...`,
        );
        router.replace(`/chat/${conversationId}`);
      } else if (!hasModelParam && !hasQueryParam) {
        console.log(
          `[NewChatPage] Received valid conversationId without model/query params. Redirecting...`,
        );
        router.replace(`/chat/${conversationId}`);
      } else {
        console.log(
          `[NewChatPage] Have OLD conversationId with model/query params - not redirecting to allow new chat creation`,
        );
      }
    } else if (conversationId && uuidRegex.test(conversationId) && isStreamingActiveForThisConversation) {
      console.log(
        `[NewChatPage] Delaying redirect for conversationId: ${conversationId} - streaming is active`,
      );
    }
  }, [conversationId, oldConversationId, router, searchParams, isStreamingActiveForThisConversation]);

  // Log isSetupComplete and initialQueryProcessed
  useEffect(() => {
    console.log(
      `[NewChatPage] useEffect (flags): isSetupComplete=${isSetupComplete}, initialQueryProcessed=${initialQueryProcessed}`,
    );
  }, [isSetupComplete, initialQueryProcessed]);

  // Log selectedModel and initialQuery
  useEffect(() => {
    console.log(
      `[NewChatPage] useEffect (params): selectedModel=${selectedModel?.model_name}, initialQuery=${initialQuery}`,
    );
  }, [selectedModel, initialQuery]);

  // Effect to send initial query from URL parameter
  useEffect(() => {
    // Ensure setup is done, model is selected, query exists, it hasn't been processed, and no messages exist yet
    if (
      isSetupComplete &&
      selectedModel &&
      initialQuery &&
      !initialQueryProcessed &&
      messages.length === 0
    ) {
      console.log(
        `Processing initial query from URL: "${initialQuery}" with model ${selectedModel.display_name}`,
      );

      // Apply pending parameters if any
      if (pendingParameters) {
        dispatch(applyPendingParameters());
      }

      const userMessage: Conversation = {
        role: 'user',
        content: initialQuery,
        messageId: uuidv4(),
        timestamp: Date.now(),
        model_display_name: selectedModel.display_name,
        model_api_name: selectedModel.model_name,
        usedMention: initialUsedMention, // Track if @mention was used on homepage
      };
      dispatch(addUserMessage(userMessage));

      // Use pending parameters if available, otherwise use current values
      const effectiveInstructions = pendingParameters?.instructions ?? currentInstructions;
      const effectivePastMessages = pendingParameters?.pastMessagesCount ?? currentPastMessages;
      const effectiveMaxTokens = pendingParameters?.maxResponseTokens ?? currentMaxTokens;
      const effectiveTemperature = pendingParameters?.temperature ?? currentTemperature;
      const effectiveTopP = pendingParameters?.topP ?? currentTopP;

      const requestBody: Omit<ChatCompletionRequest, 'stream'> = {
        prompt: initialQuery,
        model: selectedModel.model_name,
        useGoogle: false, // Default useGoogle to false for initial query? Or make configurable?
        files: undefined, // No files for initial query from URL
        instructions: initialSystemInstruction || effectiveInstructions || undefined, // Use initialSystemInstruction first, then effective instructions
        usedMention: initialUsedMention, // Track if @mention was used on homepage
        pastMessagesCount: effectivePastMessages ?? undefined,
        maxResponseTokens: effectiveMaxTokens ?? undefined,
        temperature: effectiveTemperature ?? undefined,
        topP: effectiveTopP ?? undefined,
      };

      triggerChatCompletion(requestBody)
        .unwrap()
        .catch((error: unknown) => {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          console.error(
            'Failed to initiate chat completion for initial query:',
            errorMessage,
            error,
          );
          // Optionally dispatch an error message to the chat UI
          // dispatch(setChatErrorMessage(`Failed to process initial query: ${errorMessage}`));
        });

      setInitialQueryProcessed(true); // Mark as processed
    }
  }, [
    isSetupComplete,
    selectedModel,
    initialQuery,
    initialPromptContent, // Add initialPromptContent to dependencies
    initialSystemInstruction, // Add initialSystemInstruction to dependencies
    initialUsedMention, // Add initialUsedMention to dependencies
    initialQueryProcessed,
    messages.length,
    dispatch,
    triggerChatCompletion,
    pendingParameters,
    currentInstructions,
    currentPastMessages,
    currentMaxTokens,
    currentTemperature,
    currentTopP,
  ]);

  // Effect to set the initial model from the URL query parameter
  useEffect(() => {
    if (isSetupComplete && availableModels && availableModels.length > 0) {
      const modelNameFromQuery = searchParams.get('model');
      if (modelNameFromQuery) {
        // Find the full model object from available models
        const matchingModel = availableModels.find(
          (m) => m.model_name === modelNameFromQuery,
        );

        if (matchingModel) {
          // Set both chatSlice (for ChatInputArea dropdown) and modelSlice (for other components)
          dispatch(setSelectedNextModelName(modelNameFromQuery));
          dispatch(setModel(matchingModel));
        } else {
          // Optionally set a default model if the requested one doesn't exist
          if (availableModels.length > 0) {
            const defaultModel = availableModels[0];
            dispatch(setSelectedNextModelName(defaultModel.model_name));
            dispatch(setModel(defaultModel));
          }
        }
      } else {
        // Optionally set a default model if none is provided in the URL
        if (availableModels.length > 0) {
          const defaultModel = availableModels[0];
          dispatch(setSelectedNextModelName(defaultModel.model_name));
          dispatch(setModel(defaultModel));
        }
      }
    }
  }, [isSetupComplete, searchParams, dispatch, availableModels]); // Added availableModels dependency

  // Clear sessionStorage after initial load
  useEffect(() => {
    if (typeof window !== 'undefined' && initialQueryProcessed) {
      sessionStorage.removeItem('promptContent');
      sessionStorage.removeItem('systemInstruction');
    }
  }, [initialQueryProcessed]);

  // Effect to keep the ref updated with the latest state
  useEffect(() => {
    tempFileListRef.current = tempFileList;
  }, [tempFileList]);

  // --- Input Area Handlers (Copied from [chatId]/page.tsx) ---
  const handleReset = useCallback(() => {
    dispatch(triggerClearMessage());
    if (messageInputRef.current) messageInputRef.current.value = '';
    setTempFileList([]);
    dispatch(setIsThinking(false)); // Reset thinking state
    // TODO: Cancel ongoing chatCompletionTrigger if possible
  }, [dispatch]);

  const handleStopGenerating = useCallback(() => {
    // TODO: Implement cancellation using RTK Query mutation's abortController/signal
    dispatch(setIsThinking(false));
    console.warn('Stop generating functionality needs implementation.');
  }, [dispatch]);

  const handleTextChange = useCallback(() => {
    /* Input change logic if needed */
  }, []);

  // --- Main Submit Logic (Copied from [chatId]/page.tsx) ---
  const handleSubmit = useCallback(
    async (e?: FormEvent<HTMLFormElement> | MouseEvent<HTMLButtonElement>) => {
      e?.preventDefault();

      const rawMessageContent = messageInputRef.current?.value?.trim() || '';
      // Strip selected model mentions from the message content
      const messageContent = stripSelectedModelMentions(
        rawMessageContent,
        selectedModelMentions,
        availableModels,
      );

      // Read from ref inside the callback
      const currentFiles = tempFileListRef.current;
      // Use selectedModel directly from Redux state
      if (!selectedModel || (!messageContent && currentFiles.length === 0)) {
        dispatch(
          setChatErrorMessage('Please enter a message or select a model.'),
        );
        return;
      }

      dispatch(setChatErrorMessage(undefined)); // Clear previous errors

      // --- Prepare file data ---
      let uploadedFilesData: {
        filename: string;
        mimeType: string;
        content: string;
      }[] = [];
      // Use the ref's current value here
      if (currentFiles.length > 0) {
        try {
          uploadedFilesData = await Promise.all(
            currentFiles.map(async (file) => {
              // Iterate over currentFiles from ref
              const base64Content = await readFileAsBase64(file);
              return {
                filename: file.name,
                mimeType: file.type,
                content: base64Content,
              };
            }),
          );
        } catch (error) {
          console.error('Error reading files:', error);
          dispatch(setChatErrorMessage('Failed to read files for upload.'));
          return; // Stop submission if file reading fails
        }
      }

      // --- Add user message to UI (include file indication) ---
      // Use the ref's current value here too
      const attachmentsData = currentFiles.map((f) => ({
        name: f.name,
        type: f.type,
        size: f.size,
      }));

      // Check if any model was mentioned via @mention
      const wasAnyModelMentioned = selectedModelMentions.size > 0;
      const mentionedModelName = wasAnyModelMentioned ? Array.from(selectedModelMentions.keys())[0] : null;

      const userMessage: Conversation = {
        role: 'user',
        content: messageContent, // Only include the typed text content
        attachments: attachmentsData.length > 0 ? attachmentsData : undefined, // Add structured attachment data
        messageId: uuidv4(),
        timestamp: Date.now(),
        model_display_name: selectedModel.display_name,
        model_api_name: selectedModel.model_name,
        usedMention: wasAnyModelMentioned,
      };
      
      // Check if this is the first message BEFORE adding it
      const isFirstMessage = messages.length === 0;
      
      dispatch(addUserMessage(userMessage));

      // Apply pending parameters if any (for the first message in a new conversation)
      if (pendingParameters && isFirstMessage) {
        dispatch(applyPendingParameters());
      }

      // Use pending parameters if available (for first message), otherwise use current values
      const effectiveInstructions = (pendingParameters && isFirstMessage) 
        ? pendingParameters.instructions 
        : currentInstructions;
      const effectivePastMessages = (pendingParameters && isFirstMessage)
        ? pendingParameters.pastMessagesCount
        : currentPastMessages;
      const effectiveMaxTokens = (pendingParameters && isFirstMessage)
        ? pendingParameters.maxResponseTokens
        : currentMaxTokens;
      const effectiveTemperature = (pendingParameters && isFirstMessage)
        ? pendingParameters.temperature
        : currentTemperature;
      const effectiveTopP = (pendingParameters && isFirstMessage)
        ? pendingParameters.topP
        : currentTopP;

      // Prepare body for the chatCompletion endpoint (align with [chatId]/page.tsx)
      const requestBody: Omit<ChatCompletionRequest, 'stream'> = {
        prompt: messageContent, // Send only the text prompt here
        model: selectedModel.model_name, // Use model from Redux
        // chat_session_id: undefined, // No existing ID for new chat
        useGoogle: useGoogleEnabled,
        instructions: systemInstruction || effectiveInstructions || undefined, // Use state systemInstruction first, then effective instructions
        files: uploadedFilesData.length > 0 ? uploadedFilesData : undefined, // Add files data
        usedMention: wasAnyModelMentioned, // Track if user used @mention
        pastMessagesCount: effectivePastMessages ?? undefined,
        maxResponseTokens: effectiveMaxTokens ?? undefined,
        temperature: effectiveTemperature ?? undefined,
        topP: effectiveTopP ?? undefined,
      };


      // Clear input field and temp file list
      if (messageInputRef.current) messageInputRef.current.value = '';
      setPromptContent('');
      setTempFileList([]); // Clear the state after processing files from ref

      // Clear selected model mentions after successful submission
      if (clearSelectedMentionsRef.current) {
        clearSelectedMentionsRef.current();
      }

      try {
        await triggerChatCompletion(requestBody).unwrap();
      } catch (error: any) {
        // Extract error message from RTK Query error structure
        const baseErrorMessage = error?.data?.message || error?.data || error?.message || 'Chat completion request failed';
        const errorMessage = `[${selectedModel?.display_name || 'Unknown Model'}] ${baseErrorMessage}`;
        console.error(
          'Failed to initiate chat completion:',
          errorMessage,
          error,
        );
        dispatch(setChatErrorMessage(errorMessage));
      }
    },
    [
      selectedModel,
      dispatch,
      selectedModelMentions, // Add selectedModelMentions to dependencies
      useGoogleEnabled,
      triggerChatCompletion,
      systemInstruction, // Add systemInstruction to dependencies
      availableModels, // Add availableModels to dependencies
      pendingParameters,
      messages.length,
      currentInstructions,
      currentPastMessages,
      currentMaxTokens,
      currentTemperature,
      currentTopP,
    ],
  );

  // Handler for selecting a prompt from the gallery
  const handlePromptSelectFromGallery = useCallback(
    (prompt: { prompt_content: string; system_instruction: string }) => {
      console.log('Prompt selected in new chat page:', prompt.prompt_content);
      setPromptContent(prompt.prompt_content);
      setSystemInstruction(prompt.system_instruction);
      setIsPromptGalleryModalOpen(false); // Close modal after selection
    },
    [],
  );

  const handleOpenPromptAssistant = useCallback((currentPrompt: string) => {
    setPromptAssistantInitialContent(currentPrompt);
    setIsPromptAssistantOpen(true);
  }, []);

  const handleClosePromptAssistant = useCallback(() => {
    setIsPromptAssistantOpen(false);
    setPromptAssistantInitialContent('');
  }, []);

  const handleAcceptRewrittenPrompt = useCallback(
    (newPrompt: string) => {
      setPromptContent(newPrompt);
      handleClosePromptAssistant();
    },
    [setPromptContent, handleClosePromptAssistant],
  );

  // Model Parameters Modal Handlers
  const handleOpenParamsModal = useCallback(() => {
    setIsParamsModalOpen(true);
  }, []);

  const handleCloseParamsModal = useCallback(() => {
    setIsParamsModalOpen(false);
  }, []);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Enter' && !e.shiftKey && !isSubmitting) {
        e.preventDefault();
        handleSubmit();
      }
    },
    [isSubmitting, handleSubmit],
  );

  // Conditional Rendering for loading state from Redux store
  const LoadingIndicator = () =>
    isAiTyping ? (
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', px: 1, mb: 2 }}>
      </Box>
    ) : null;

  // Handle case where no model is selected in Redux
  if (!selectedModel && isSetupComplete) {
    // Check isSetupComplete to avoid showing error during initial load
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please select a model from the homepage to start a new chat.
        </Alert>
      </Box>
    );
  }

  // Wait until setup (clearing messages) is complete before rendering
  if (!isSetupComplete) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          p: 3,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Render the full chat interface structure
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100svh',
        bgcolor: 'background.default',
        position: 'relative',
      }}
    >
      {/* Use selectedModel.display_name for the header */}
      <ChatHeader
        chatId={'new'}
        chatTopic={selectedModel?.display_name || 'New Chat'}
        onOpenParameters={handleOpenParamsModal} // Use proper handler
      />
      <Box sx={{ flexGrow: 1, overflowY: 'auto', p: 2 }}>
        {/* Add placeholder for onOpenSourcesPanel */}
        <ConversationDisplay
          messages={messages}
          onOpenSourcesPanel={() =>
            console.log('Open sources panel requested (new chat)')
          }
          isHistoryLoading={false}
        />
        <LoadingIndicator />
        <ChatErrorDisplay />
      </Box>
      <Box
        sx={{
          p: 2,
          zIndex: 1,
        }}
      >
        <ChatInputArea
          isLoading={isSubmitting}
          // gptModel={selectedModel} // Removed unused prop
          tempFileList={tempFileList}
          setTempFileList={setTempFileList}
          handleReset={handleReset}
          handleSubmit={handleSubmit}
          onTextChange={handleTextChange}
          onKeyDown={handleKeyDown}
          supportedFileList={supportedFileList || ''}
          visionModelList={visionModelList}
          disableFileUpload={false} // Or make dynamic
          onClickStopButton={handleStopGenerating}
          uploadFileSizeLimit={parseInt(
            process.env.NEXT_PUBLIC_UPLOAD_FILE_SIZE_LIMIT || '10000000',
            10,
          )}
          sizeExceedsMessage={'File size exceeds limit.'}
          setChatErrorMessage={(msg: string | undefined) =>
            dispatch(setChatErrorMessage(msg))
          }
          messageInputRef={messageInputRef}
          useGoogleEnabled={useGoogleEnabled}
          setUseGoogleEnabled={setUseGoogleEnabled}
          onSelectedMentionsChange={setSelectedModelMentions}
          clearSelectedMentionsRef={clearSelectedMentionsRef}
          promptContent={promptContent}
          setPromptContent={setPromptContent}
          systemInstruction={systemInstruction} // Pass systemInstruction
          setSystemInstruction={setSystemInstruction} // Pass setSystemInstruction
          onPromptSelect={() => {
            console.log('Prompt gallery button clicked in new chat page');
            setIsPromptGalleryModalOpen(true);
          }} // Open modal on click
          onPromptAssistantClick={handleOpenPromptAssistant}
        />
      </Box>

      {/* Prompt Gallery Modal */}
      <PromptGalleryModal
        open={isPromptGalleryModalOpen}
        handleClose={() => setIsPromptGalleryModalOpen(false)}
        handlePromptSelect={handlePromptSelectFromGallery}
        isExistingChat={true} // Treat new chat page as existing chat for prompt population
      />

      {/* Render the Prompt Rewrite Assistant Modal */}
      <PromptRewriteAssistantModal
        open={isPromptAssistantOpen}
        onClose={handleClosePromptAssistant}
        currentPrompt={promptAssistantInitialContent}
        onApply={handleAcceptRewrittenPrompt}
        onRewrite={async (prompt) => {
          const response = await rewritePrompt({ prompt }).unwrap();
          return response.prompt;
        }}
        onGenerate={async (idea) => {
          const response = await generatePrompt({ idea }).unwrap();
          return response.prompt;
        }}
      />

      {/* Render the Model Parameters Modal */}
      <ModelParametersModal
        open={isParamsModalOpen}
        onClose={handleCloseParamsModal}
      />
    </Box>
  );
}

// Main page component using Suspense for searchParams
export default function NewChatPage() {
  // The layout is now handled by chat/layout.tsx
  // This component only needs to render the content for the main area

  return (
    // Render the content directly, layout.tsx will place it correctly
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            p: 3,
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <NewChatPageContent />
    </Suspense>
  );
}
