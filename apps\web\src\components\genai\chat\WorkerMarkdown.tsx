import React, { useState, useEffect, useMemo } from 'react';
import { Box, CircularProgress } from '@mui/material';
import { convertBracketedLatexToMarkdownMath } from '@/lib/utils/convertBracketedLatexToMarkdownMath';

const WorkerMarkdown = ({ content }: { content: string }) => {
  const [html, setHtml] = useState('');
  const [loading, setLoading] = useState(true);

  const worker = useMemo(() => {
    return new Worker(new URL('../../../workers/markdown.worker.ts', import.meta.url));
  }, []);

  useEffect(() => {
    setLoading(true);
    worker.postMessage(convertBracketedLatexToMarkdownMath(content));

    worker.onmessage = (event: MessageEvent<string>) => {
      setHtml(event.data);
      setLoading(false);
    };

    return () => {
      worker.terminate();
    };
  }, [content, worker]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  return <div dangerouslySetInnerHTML={{ __html: html }} />;
};

export default WorkerMarkdown;