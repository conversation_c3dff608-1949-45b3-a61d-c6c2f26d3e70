import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ImageStorageService {
  private readonly logger = new Logger(ImageStorageService.name);

  constructor(private configService: ConfigService) {}

  /**
   * Saves an image to the shared drive with the specified directory structure
   * @param base64Content Base64 encoded image content
   * @param mimeType MIME type of the image
   * @param filename Original filename
   * @param ssoid User's SSO ID
   * @returns Promise<string> - The relative path to the saved image
   */
  async saveImageToSharedDrive(
    base64Content: string,
    mimeType: string,
    filename: string,
    ssoid: string,
  ): Promise<string> {
    try {
      // Get base path from environment
      const basePath = this.configService.get<string>('BASE64_FILE_PATH');
      if (!basePath) {
        throw new Error('BASE64_FILE_PATH environment variable not configured');
      }

      // Generate unique image ID
      const imageId = uuidv4();
      const fileExtension = this.getFileExtension(mimeType, filename);
      const imageFileName = `${imageId}${fileExtension}`;

      // Create directory structure: {basePath}/YYYY-MM-DD/{ssoid}/
      const dateFolder = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const fullDirectoryPath = path.join(basePath, dateFolder, ssoid);

      // Ensure directory exists
      await this.ensureDirectoryExists(fullDirectoryPath);

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(base64Content, 'base64');

      // Full file path
      const fullFilePath = path.join(fullDirectoryPath, imageFileName);

      // Write file to shared drive (no encryption for now)
      await fs.writeFile(fullFilePath, imageBuffer);
      this.logger.debug(`Image ${imageFileName} stored without encryption`);

      // Return relative path for database storage
      const relativePath = path.join(dateFolder, ssoid, imageFileName);
      
      this.logger.log(
        `Image saved successfully: ${filename} -> ${relativePath} (${imageBuffer.length} bytes)`,
      );

      return relativePath;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : String(error);

      this.logger.error(
        `Failed to save image ${filename} for user ${ssoid}:`,
        errorStack,
      );
      throw new Error(`Failed to save image to shared drive: ${errorMessage}`);
    }
  }

  /**
   * Retrieves an image from the shared drive
   * @param relativePath Relative path to the image
   * @param ssoid User's SSO ID (for security validation)
   * @returns Promise<Buffer> - The image buffer
   */
  async getImageFromSharedDrive(
    relativePath: string,
    ssoid: string,
  ): Promise<Buffer> {
    try {
      const basePath = this.configService.get<string>('BASE64_FILE_PATH');
      if (!basePath) {
        throw new Error('BASE64_FILE_PATH environment variable not configured');
      }

      // Security check: ensure the path contains the user's ssoid
      if (!relativePath.includes(ssoid)) {
        throw new Error('Access denied: Invalid path for user');
      }

      const fullFilePath = path.join(basePath, relativePath);

      // Check if file exists
      try {
        await fs.access(fullFilePath);
      } catch {
        throw new Error('Image file not found');
      }

      // Read file (no decryption needed)
      const fileData = await fs.readFile(fullFilePath);
      return fileData;
    } catch (error) {
      const errorStack = error instanceof Error ? error.stack : String(error);

      this.logger.error(
        `Failed to retrieve image ${relativePath} for user ${ssoid}:`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Deletes an image from the shared drive
   * @param relativePath Relative path to the image
   * @param ssoid User's SSO ID (for security validation)
   */
  async deleteImageFromSharedDrive(
    relativePath: string,
    ssoid: string,
  ): Promise<void> {
    try {
      const basePath = this.configService.get<string>('BASE64_FILE_PATH');
      if (!basePath) {
        throw new Error('BASE64_FILE_PATH environment variable not configured');
      }

      // Security check: ensure the path contains the user's ssoid
      if (!relativePath.includes(ssoid)) {
        throw new Error('Access denied: Invalid path for user');
      }

      const fullFilePath = path.join(basePath, relativePath);

      // Delete file
      await fs.unlink(fullFilePath);
      
      this.logger.log(`Image deleted successfully: ${relativePath}`);
    } catch (error) {
      const errorStack = error instanceof Error ? error.stack : String(error);

      this.logger.error(
        `Failed to delete image ${relativePath} for user ${ssoid}:`,
        errorStack,
      );
      throw error;
    }
  }

  /**
   * Ensures a directory exists, creating it if necessary
   */
  private async ensureDirectoryExists(directoryPath: string): Promise<void> {
    try {
      await fs.access(directoryPath);
    } catch {
      // Directory doesn't exist, create it
      await fs.mkdir(directoryPath, { recursive: true });
      this.logger.debug(`Created directory: ${directoryPath}`);
    }
  }

  /**
   * Gets the appropriate file extension based on MIME type and filename
   */
  private getFileExtension(mimeType: string, filename: string): string {
    // Try to get extension from filename first
    const filenameExt = path.extname(filename);
    if (filenameExt) {
      return filenameExt;
    }

    // Fallback to MIME type mapping
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/jpg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/bmp': '.bmp',
      'image/webp': '.webp',
      'image/tiff': '.tiff',
    };

    return mimeToExt[mimeType] || '.jpg';
  }
}
