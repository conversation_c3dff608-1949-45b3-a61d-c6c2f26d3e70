/**
 * Utility functions for determining model capabilities
 * Matches backend logic for consistent behavior
 */

export interface ModelCapabilities {
  supportsTools: boolean;
  supportsSystemMessages: boolean;
  supportsSearch: boolean;
  supportsTemperature: boolean;
  supportsThinking: boolean;
}

/**
 * Determines if a model supports tool/function calling
 * This should match the backend logic in ChatCompletionService
 */
export function getModelCapabilities(
  modelName?: string | null,
): ModelCapabilities {
  if (!modelName) {
    return {
      supportsTools: false,
      supportsSystemMessages: true,
      supportsSearch: true, // All models support search via keyword extraction
      supportsTemperature: true,
      supportsThinking: false,
    };
  }

  const modelNameLower = modelName.toLowerCase();

  // Models that explicitly don't support tools (matches backend TOOL_UNSUPPORTED_MODELS)
  const toolUnsupportedModels = [
    'o1-mini', // o1-mini specifically doesn't support function calling
    'deepseek-', // DeepSeek models hosted on Azure AI Service don't support OpenAI-compatible endpoints
  ];

  // Check if model is explicitly unsupported for tools
  const supportsTools = !toolUnsupportedModels.some((unsupported) =>
    modelNameLower.includes(unsupported),
  );

  // Models that don't support system messages (matches backend pattern)
  const systemMessageUnsupportedPatterns = [
    /^o1($|-)/, // o1, o1-mini, o1-preview, etc.
    /^o3($|-)/, // o3, o3-mini, etc.
  ];

  const supportsSystemMessages = !systemMessageUnsupportedPatterns.some(
    (pattern) => pattern.test(modelNameLower),
  );

  // Models that don't support temperature parameter (matches o1/o3 family restrictions)
  const temperatureUnsupportedPatterns = [
    /^o1($|-)/, // o1, o1-mini, o1-preview, etc.
    /^o3($|-)/, // o3, o3-mini, etc.
  ];

  const supportsTemperature = !temperatureUnsupportedPatterns.some(
    (pattern) => pattern.test(modelNameLower),
  );

  // All models support search via keyword extraction approach
  const supportsSearch = true;

  // Models that support thinking/reasoning (Gemini 2.5 Pro/Flash)
  const thinkingPatterns = [
    /^gemini-2\.5-(pro|flash)/i,
    // Add other thinking-capable models here as they become available
  ];

  const supportsThinking = thinkingPatterns.some(
    (pattern) => pattern.test(modelName),
  );

  return {
    supportsTools,
    supportsSystemMessages,
    supportsSearch,
    supportsTemperature,
    supportsThinking,
  };
}

/**
 * Checks if a model supports tool/function calling
 */
export function modelSupportsTools(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsTools;
}

/**
 * Checks if a model supports system messages
 */
export function modelSupportsSystemMessages(
  modelName?: string | null,
): boolean {
  return getModelCapabilities(modelName).supportsSystemMessages;
}

/**
 * Checks if a model supports search functionality
 */
export function modelSupportsSearch(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsSearch;
}

/**
 * Checks if a model supports temperature parameter
 */
export function modelSupportsTemperature(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsTemperature;
}

/**
 * Checks if a model supports thinking/reasoning mode
 */
export function modelSupportsThinking(modelName?: string | null): boolean {
  return getModelCapabilities(modelName).supportsThinking;
}

/**
 * Gets a user-friendly explanation for why a feature is unavailable
 */
export function getFeatureUnavailableReason(
  feature: 'search' | 'systemMessages' | 'temperature' | 'thinking',
  modelName?: string | null,
): string {
  if (!modelName) {
    const featureMap = {
      search: 'Search functionality',
      systemMessages: 'System messages',
      temperature: 'Temperature parameter',
      thinking: 'Thinking mode',
    };
    return `${featureMap[feature]} require a model to be selected`;
  }

  const capabilities = getModelCapabilities(modelName);

  if (feature === 'search' && !capabilities.supportsSearch) {
    return `Search functionality is not available for ${modelName}`;
  }

  if (feature === 'systemMessages' && !capabilities.supportsSystemMessages) {
    return `System messages are not supported by ${modelName}`;
  }

  if (feature === 'temperature' && !capabilities.supportsTemperature) {
    return `Temperature parameter is not supported by ${modelName}`;
  }

  if (feature === 'thinking' && !capabilities.supportsThinking) {
    return `Thinking mode is not supported by ${modelName}`;
  }

  return ''; // Feature is available
}
