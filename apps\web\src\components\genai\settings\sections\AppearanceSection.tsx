'use client';

import React from 'react';
import {
  Button,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { selectTheme, setTheme, ThemeMode } from '@/lib/store/themeSlice';
import { useResetTutorialMutation } from '@/lib/store/apiSlice';
import { startTour } from '@/lib/store/tutorialSlice';

const AppearanceSection = () => {
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [resetTutorial] = useResetTutorialMutation();

  const handleThemeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setTheme(event.target.value as ThemeMode));
  };

  const handleRestartTutorial = async () => {
    await resetTutorial();
    dispatch(startTour({ tour: 'main' }));
  };

  return (
    <div>
      <Typography variant="h6" gutterBottom>
        Appearance
      </Typography>
      <FormControl component="fieldset">
        <RadioGroup
          aria-label="theme"
          name="theme"
          value={currentTheme}
          onChange={handleThemeChange}
        >
          <FormControlLabel value="light" control={<Radio />} label="Light" />
          <FormControlLabel value="dark" control={<Radio />} label="Dark" />
          <FormControlLabel
            value="system"
            control={<Radio />}
            label="System Default"
          />
        </RadioGroup>
      </FormControl>
      
      {!isMobile && (
        <Button
          variant="outlined"
          onClick={handleRestartTutorial}
          sx={{ mt: 2 }}
        >
          Restart Tutorial
        </Button>
      )}
    </div>
  );
};

export default AppearanceSection;
