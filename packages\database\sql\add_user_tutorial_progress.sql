-- =============================================
-- Author:      Roo
-- Create date: 2025-08-01
-- Description: Creates the user_tutorial_progress table to track user tutorial completion.
-- =============================================

-- Drop trigger if it exists
IF OBJECT_ID('trg_user_tutorial_progress_update', 'TR') IS NOT NULL
    DROP TRIGGER trg_user_tutorial_progress_update;
GO

-- Drop table if it exists
IF OBJECT_ID('user_tutorial_progress', 'U') IS NOT NULL
    DROP TABLE user_tutorial_progress;
GO

-- Create the user_tutorial_progress table
CREATE TABLE dbo.user_tutorial_progress (
    id INT IDENTITY(1,1) NOT NULL,
    username VARCHAR(30) NOT NULL,
    completed BIT NOT NULL CONSTRAINT DF_user_tutorial_progress_completed DEFAULT 0,
    create_dt DATETIME NOT NULL CONSTRAINT DF_user_tutorial_progress_create_dt DEFAULT GETDATE(),
    update_dt DATETIME NOT NULL CONSTRAINT DF_user_tutorial_progress_update_dt DEFAULT GETDATE(),
    CONSTRAINT PK_user_tutorial_progress PRIMARY KEY CLUSTERED (id ASC),
    CONSTRAINT UQ_user_tutorial_progress_username UNIQUE NONCLUSTERED (username ASC)
);
GO

-- Add foreign key constraint to acl_user
ALTER TABLE dbo.user_tutorial_progress WITH CHECK ADD CONSTRAINT FK_user_tutorial_progress_acl_user FOREIGN KEY(username)
REFERENCES dbo.acl_user (username);
GO

ALTER TABLE dbo.user_tutorial_progress CHECK CONSTRAINT FK_user_tutorial_progress_acl_user;
GO
