import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { getHongKongTime } from '../common/utils/timezone.util';

@Injectable()
export class UserService {
  constructor(private readonly prisma: PrismaService) {}

  async completeTutorial(userId: string, tour: string): Promise<void> {
    console.log(`[UserService] Starting completeTutorial for user: ${userId}, tour: ${tour}`);
    
    try {
      // First check if user exists in tutorial progress table
      const user = await this.prisma.user_tutorial_progress.findUnique({
        where: { username: userId },
      });

      console.log(`[UserService] User tutorial progress lookup:`, {
        userId,
        userExists: !!user,
        currentCompletedTours: user?.completed_tours || 'none'
      });

      if (user) {
        // User exists, update their completed tours
        const completed_tours = user.completed_tours
          ? user.completed_tours.split(',')
          : [];
        
        console.log(`[UserService] Current completed tours:`, completed_tours);
        
        if (!completed_tours.includes(tour)) {
          completed_tours.push(tour);
          console.log(`[UserService] Adding tour "${tour}" to completed list:`, completed_tours);
          
          const updatedToursString = completed_tours.join(',');
          await this.prisma.user_tutorial_progress.update({
            where: { username: userId },
            data: {
              completed_tours: updatedToursString,
              update_dt: getHongKongTime(),
            },
          });
          
          console.log(`[UserService] Successfully updated database with completed_tours: "${updatedToursString}"`);
        } else {
          console.log(`[UserService] Tour "${tour}" already completed, no database update needed`);
        }
      } else {
        // User doesn't exist, create new record
        console.log(`[UserService] User not found in tutorial progress, creating new record`);
        
        // Check if user exists in acl_user_details table before creating tutorial progress
        const aclUserDetails = await this.prisma.acl_user_details.findUnique({
          where: { username: userId },
        });
        
        if (!aclUserDetails) {
          const errorMsg = `User ${userId} not found in acl_user_details table. Please contact an administrator to set up your account.`;
          console.error(`[UserService] ${errorMsg}`);
          throw new Error(errorMsg);
        } else {
          console.log(`[UserService] User found in acl_user_details table, creating tutorial progress record`);
        }
        
        await this.prisma.user_tutorial_progress.create({
          data: {
            username: userId,
            completed_tours: tour,
            completed: false,
            create_dt: getHongKongTime(),
          },
        });
        
        console.log(`[UserService] Successfully created tutorial progress record with completed_tours: "${tour}"`);
      }
      
      console.log(`[UserService] completeTutorial completed successfully for user: ${userId}, tour: ${tour}`);
      
    } catch (error) {
      console.error(`[UserService] Error completing tutorial for user ${userId}, tour ${tour}:`, error);
      
      // Type guard for Error objects
      if (error instanceof Error) {
        console.error(`[UserService] Error stack:`, error.stack);
      }
      
      throw error; // Re-throw error to allow frontend to handle it
    }
  }

  async resetTutorial(userId: string): Promise<void> {
    try {
      await this.prisma.user_tutorial_progress.update({
        where: { username: userId },
        data: { 
          completed: false, 
          completed_tours: '',
          update_dt: getHongKongTime(),
        },
      });
    } catch (error) {
      console.error(`Error resetting tutorial for user ${userId}:`, error);
      // Silently fail rather than throwing error
    }
  }

  async getTutorialStatus(
    userId: string,
  ): Promise<{ completed: boolean; completed_tours: string[] }> {
    try {
      const user = await this.prisma.user_tutorial_progress.findUnique({
        where: { username: userId },
      });

      return {
        completed: user?.completed ?? false,
        completed_tours: user?.completed_tours?.split(',').filter(Boolean) ?? [],
      };
    } catch (error) {
      console.error(`Error getting tutorial status for user ${userId}:`, error);
      return {
        completed: false,
        completed_tours: [],
      };
    }
  }

  async getWelcomeStatus(userId: string): Promise<{ notified: boolean }> {
    try {
      const notifyRecord = await this.prisma.notify.findFirst({
        where: { ssoid: userId },
      });

      return { notified: !!notifyRecord };
    } catch (error) {
      console.error(`Error getting welcome status for user ${userId}:`, error);
      return { notified: false };
    }
  }

  async markWelcomeSeen(userId: string): Promise<{ success: boolean }> {
    try {
      const existingNotify = await this.prisma.notify.findFirst({
        where: { ssoid: userId },
        select: { notify_id: true },
      });

      if (existingNotify) {
        await this.prisma.notify.update({
          where: { notify_id: existingNotify.notify_id },
          data: {
            notified: 'Y',
            update_dt: getHongKongTime(),
          },
        });
      } else {
        await this.prisma.notify.create({
          data: {
            ssoid: userId,
            notified: 'Y',
            create_by: 'system',
            create_dt: getHongKongTime(),
          },
        });
      }

      return { success: true };
    } catch (error) {
      console.error(`Error marking welcome as seen for user ${userId}:`, error);
      return { success: false };
    }
  }
}