'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Slider,
  Box,
  Typography,
  Grid,
  Input,
  Tooltip,
  IconButton,
  useMediaQuery,
  useTheme,
  SwipeableDrawer,
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useAppSelector, useAppDispatch } from '@/lib/store/hooks';
import {
  selectConversationId,
  selectInstructions,
  selectPastMessagesCount,
  selectMaxResponseTokens,
  selectTemperature,
  selectTopP,
  setConversationParameters, // Action to update Redux state (used optimistically)
  setPendingParameters,
  selectPendingParameters,
} from '@/lib/store/chatSlice';
import { useUpdateConversationParamsMutation } from '@/lib/store/apiSlice';
import { selectCurrentGptModel } from '@/lib/store/modelSlice';
import { 
  modelSupportsTemperature, 
  getFeatureUnavailableReason 
} from '@/lib/utils/modelCapabilities';

interface ModelParametersModalProps {
  open: boolean;
  onClose: () => void;
  conversationId?: string; // Add optional conversationId prop
}

// Helper to ensure value is within slider bounds
const valueInRange = (
  value: number | null | undefined,
  min: number,
  max: number,
  defaultValue: number,
): number => {
  const num = value ?? defaultValue;
  return Math.max(min, Math.min(max, num));
};

const ModelParametersModal: React.FC<ModelParametersModalProps> = ({
  open,
  onClose,
  conversationId: propConversationId,
}) => {
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Get current values from Redux store
  const reduxConversationId = useAppSelector(selectConversationId);
  const currentInstructions = useAppSelector(selectInstructions);
  const currentPastMessages = useAppSelector(selectPastMessagesCount);
  const currentMaxTokens = useAppSelector(selectMaxResponseTokens);
  const currentTemperature = useAppSelector(selectTemperature);
  const currentTopP = useAppSelector(selectTopP);
  const pendingParams = useAppSelector(selectPendingParameters);
  const currentModel = useAppSelector(selectCurrentGptModel);
  
  // Use prop conversationId if provided, fallback to Redux state
  const conversationId = propConversationId || reduxConversationId;
  
  // Model capability detection
  const supportsTemperature = modelSupportsTemperature(currentModel?.model_name);
  const temperatureUnavailableReason = getFeatureUnavailableReason('temperature', currentModel?.model_name);
  

  // Local state for form values, initialized from Redux state
  const [instructions, setInstructions] = useState(currentInstructions ?? '');
  // Past Messages: Default 10, Max 20
  const [pastMessages, setPastMessages] = useState<number>(
    valueInRange(currentPastMessages, 0, 20, 10),
  );
  // Max Tokens: Default 2000, Max 4096
  const [maxTokens, setMaxTokens] = useState<number | ''>(
    currentMaxTokens ?? 2000,
  ); // Set default 2000, allow empty
  // Temperature: Default 0.6, Max 1.0
  const [temperature, setTemperature] = useState<number>(
    valueInRange(currentTemperature, 0, 1, 0.6),
  );
  // Top P: Default 0.6, Max 1.0
  const [topP, setTopP] = useState<number>(
    valueInRange(currentTopP, 0, 1, 0.6),
  );

  // RTK Query Mutation hook
  const [updateParams, { isLoading, isError, error }] =
    useUpdateConversationParamsMutation();

  // Update local state if Redux state changes while modal is open
  useEffect(() => {
    
    setInstructions(currentInstructions ?? '');
    // Past Messages: Default 10, Max 20
    setPastMessages(valueInRange(currentPastMessages, 0, 20, 10));
    // Max Tokens: Default 2000, Max 4096
    setMaxTokens(currentMaxTokens ?? 2000); // Use default 2000 if null/undefined
    // Temperature: Default 0.6, Max 1.0
    setTemperature(valueInRange(currentTemperature, 0, 1, 0.6));
    // Top P: Default 0.6, Max 1.0
    setTopP(valueInRange(currentTopP, 0, 1, 0.6));
  }, [
    currentInstructions,
    currentPastMessages,
    currentMaxTokens,
    currentTemperature,
    currentTopP,
    open,
    conversationId,
  ]);

  const handleSliderChange =
    (setter: React.Dispatch<React.SetStateAction<number>>) =>
    (event: Event, newValue: number | number[]) => {
      setter(newValue as number);
    };

  // Use a generic for allowEmpty to correctly type the setter
  const handleInputChange =
    <TAllowEmpty extends boolean = false>(
      setter: React.Dispatch<
        React.SetStateAction<TAllowEmpty extends true ? number | '' : number>
      >,
      min: number,
      max: number,
      allowEmpty?: TAllowEmpty, // Make allowEmpty optional, defaults to false
    ) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      if (value === '' && allowEmpty) {
        // Explicitly cast setter when allowEmpty is true
        (setter as React.Dispatch<React.SetStateAction<number | ''>>)('');
      } else {
        const numValue = Number(value);
        if (!isNaN(numValue)) {
          // Cast setter to number only when setting a number
          (setter as React.Dispatch<React.SetStateAction<number>>)(
            Math.max(min, Math.min(max, numValue)),
          );
        }
      }
    };

  // Use a generic for allowEmpty to correctly type the setter
  const handleBlur =
    <TAllowEmpty extends boolean = false>(
      value: TAllowEmpty extends true ? number | '' : number, // Value type depends on allowEmpty
      setter: React.Dispatch<
        React.SetStateAction<TAllowEmpty extends true ? number | '' : number>
      >,
      defaultValue: number,
      allowEmpty?: TAllowEmpty, // Make allowEmpty optional, defaults to false
    ) =>
    () => {
      if (value === '' && !allowEmpty) {
        setter(defaultValue);
      }
    };

  const handleSave = async () => {
    const paramsToUpdate = {
      instructions: instructions, // Keep the actual value, even if empty string
      pastMessagesCount: pastMessages,
      maxResponseTokens: maxTokens === '' ? null : Number(maxTokens), // Send null if empty
      temperature: temperature,
      topP: topP,
    };


    if (!conversationId) {
      // No conversation ID yet - save as pending parameters
      dispatch(setPendingParameters(paramsToUpdate));
      onClose();
      return;
    }

    // Have conversation ID - save to backend
    try {
      const apiPayload = {
        chat_session_id: conversationId,
        ...paramsToUpdate,
      };
      
      
      // The mutation hook handles optimistic update via onQueryStarted in apiSlice
      await updateParams(apiPayload).unwrap();
      
      onClose(); // Close modal on success
    } catch (err) {
      // Error state is handled by `isError` and `error` from the hook
      // TODO: Display a user-friendly error message based on `error`
    }
  };

  const modalContent = (
    <Box sx={{ maxHeight: '80vh', overflowY: 'auto' }}>
      <DialogTitle>Model Parameters</DialogTitle>
      <DialogContent>
        {/* Instructions */}
        <Box sx={{ mb: 2 }}>
          <Typography
            gutterBottom
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            Give the model instructions and context
            <Tooltip
              title="Provide custom instructions or system prompt for the AI model."
              placement="top"
            >
              <InfoOutlinedIcon
                sx={{ ml: 0.5, fontSize: '1rem', color: 'action.active' }}
              />
            </Tooltip>
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder="e.g., You are a helpful assistant specializing in Hong Kong history."
          />
        </Box>

        {/* Past Messages */}
        <ParameterSlider
          label="Past messages included"
          tooltip="Number of previous messages to include in the context for the next response."
          value={pastMessages}
          onChange={handleSliderChange(setPastMessages)}
          onInputChange={handleInputChange(setPastMessages, 0, 20)} // Max 20
          onBlur={handleBlur(pastMessages, setPastMessages, 10)} // Default 10
          min={0}
          max={20}
          step={1} // Max 20
        />

        {/* Max Response Tokens */}
        <ParameterSlider
          label="Max response tokens"
          tooltip="Maximum number of tokens the model can generate in its response. Leave empty for model default."
          value={maxTokens}
          onChange={(e, nv) => setMaxTokens(nv as number)} // Slider always gives number
          onInputChange={handleInputChange(setMaxTokens, 1, 4096, true)} // Max 4096, Allow empty
          onBlur={handleBlur(maxTokens, setMaxTokens, 2000, true)} // Default 2000
          min={1}
          max={4096}
          step={64} // Max 4096, adjust step
        />

        {/* Temperature */}
        <ParameterSlider
          label="Temperature"
          tooltip="Controls randomness (0.0 - 2.0). Lower values are more deterministic, higher values are more creative."
          value={temperature}
          onChange={handleSliderChange(setTemperature)}
          onInputChange={handleInputChange(setTemperature, 0, 1)} // Max 1.0
          onBlur={handleBlur(temperature, setTemperature, 0.6)} // Default 0.6
          min={0}
          max={1}
          step={0.1} // Step is 0.1
        />
        {!supportsTemperature && (
          <Typography variant="caption" color="warning.main" sx={{ mt: -1, mb: 1, display: 'block', fontStyle: 'italic' }}>
            ⚠️ Temperature parameter is not supported by o1 and o3-mini models - this parameter will be ignored.
          </Typography>
        )}

        {/* Top P */}
        <ParameterSlider
          label="Top P"
          tooltip="Nucleus sampling (0.0 - 1.0). Considers only tokens comprising the top P probability mass."
          value={topP}
          onChange={handleSliderChange(setTopP)}
          onInputChange={handleInputChange(setTopP, 0, 1)} // Max 1.0
          onBlur={handleBlur(topP, setTopP, 0.6)} // Default 0.6
          min={0}
          max={1}
          step={0.1} // Step is 0.1
        />


        {isError && (
          <Typography color="error" variant="body2">
            Failed to save parameters: {JSON.stringify(error)}
          </Typography>
        )}
        
        {!conversationId && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Parameters will be applied when the conversation starts.
            </Typography>
          </Box>
        )}
      </DialogContent>
      {/* Left-align buttons, add specific styling */}
      <DialogActions sx={{ px: 3, pb: 2, justifyContent: 'flex-end', gap: 1 }}>
        <Button
          onClick={handleSave}
          disabled={isLoading}
          color="primary"
          variant="contained"
          sx={{ borderRadius: '16px' }}
        >
          {isLoading ? 'Saving...' : (!conversationId ? 'Apply' : 'Save')}
        </Button>
      </DialogActions>
    </Box>
  );

  return isMobile ? (
    <SwipeableDrawer
      anchor="bottom"
      open={open}
      onClose={onClose}
      onOpen={() => {}} // Required prop for SwipeableDrawer
      PaperProps={{
        sx: {
          borderTopLeftRadius: 16, // Apply border-radius to top-left corner
          borderTopRightRadius: 16, // Apply border-radius to top-right corner
          backdropFilter: 'blur(10px)', // Add blur effect
          backgroundColor: 'background.paper', // Ensure solid background
          zIndex: 5000,
        },
      }}
      disableSwipeToOpen={false}
      ModalProps={{
        keepMounted: true,
      }}
    >
      <Box
        onClick={onClose} // Add onClick to close the drawer
        sx={{
          display: 'flex',
          justifyContent: 'center',
          pt: 1, // Padding top for the handle
          pb: 0.5, // Padding bottom for the handle
          cursor: 'pointer', // Indicate it's clickable
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 4,
            borderRadius: 2,
            bgcolor: 'text.secondary', // Color of the handle
          }}
        />
      </Box>
      {modalContent}
    </SwipeableDrawer>
  ) : (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          border: '1px solid',
          borderColor: 'divider',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        },
      }}
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(3px)',
          backgroundColor: 'rgba(0,0,0,0.1)',
        },
      }}
    >
      {modalContent}
    </Dialog>
  );
};

// Slider/Input component structure
const ParameterSlider = ({
  label,
  tooltip,
  value,
  onChange,
  onInputChange,
  onBlur,
  min,
  max,
  step,
  inputWidth = 70,
}: {
  label: string;
  tooltip: string;
  value: number | '';
  onChange: (event: Event, newValue: number) => void;
  onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: () => void;
  min: number;
  max: number;
  step: number;
  inputWidth?: number;
}) => (
  <Box sx={{ mb: 2 }}>
    <Typography gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
      {label}
      <Tooltip title={tooltip} placement="top">
        <InfoOutlinedIcon
          sx={{ ml: 0.5, fontSize: '1rem', color: 'action.active' }}
        />
      </Tooltip>
    </Typography>
    <Grid container alignItems="center" justifyContent={'space-between'}>
      <Grid size={9}>
        <Slider
          value={typeof value === 'number' ? value : 0}
          onChange={onChange}
          aria-labelledby={`${label}-slider`} //test
          min={min}
          max={max}
          step={step} // Re-add step prop
        />
      </Grid>
      <Grid size={2}>
        <Input
          value={value}
          size="small"
          onChange={onInputChange}
          onBlur={onBlur}
          inputProps={{
            step: step,
            min: min,
            max: max,
            type: 'number',
            'aria-labelledby': `${label}-slider`,
            style: { textAlign: 'center' }, // Center align input text
          }}
          sx={{ width: inputWidth, maxWidth: '100%' }}
        />
      </Grid>
    </Grid>
  </Box>
);

export default ModelParametersModal;
